#!/usr/bin/env python3
"""
Simple PDF Protection Generator
One-click solution to create a self-destruct protected PDF

Usage: python create_protected_pdf.py
"""

import os
import sys
import shutil
from datetime import datetime
import PyPDF2
import hashlib

def create_protected_copy():
    """Create a protected copy of main.pdf with self-destruct viewer"""
    
    print("🔒 PDF Protection Generator")
    print("=" * 40)
    
    # Configuration
    input_pdf = "main.pdf"
    author_name = "<PERSON><PERSON><PERSON><PERSON>"
    thesis_title = "Considérations fiscales et financières - Piano Mattei"
    
    # Check if input file exists
    if not os.path.exists(input_pdf):
        print(f"❌ Error: {input_pdf} not found!")
        print("Make sure main.pdf is in the current directory.")
        return False
    
    # Generate output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_pdf = f"main_PROTECTED_SELFDESTRUCT_{timestamp}.pdf"
    
    print(f"📄 Input: {input_pdf}")
    print(f"📁 Output: {output_pdf}")
    print(f"👤 Author: {author_name}")
    print()
    
    try:
        # Generate passwords (same as your existing tools)
        year = datetime.now().year
        user_password = f"Print{year}Only"
        base_string = f"{author_name}_{input_pdf}_{datetime.now().strftime('%Y%m%d')}"
        owner_password = hashlib.md5(base_string.encode()).hexdigest()[:16]
        
        print("🔐 Generating secure PDF...")
        
        # Read and encrypt PDF
        with open(input_pdf, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            writer = PyPDF2.PdfWriter()
            
            total_pages = len(reader.pages)
            print(f"📄 Processing {total_pages} pages...")
            
            # Copy all pages
            for page_num, page in enumerate(reader.pages, 1):
                if page_num % 50 == 0:
                    print(f"⚙️  Processing page {page_num}/{total_pages}")
                writer.add_page(page)
            
            # Apply strong encryption
            try:
                writer.encrypt(
                    user_password=user_password,
                    owner_password=owner_password,
                    use_128bit=True,
                    permissions_flag=4  # Print only
                )
            except Exception as e:
                print(f"⚠️  Using alternative encryption: {e}")
                writer.encrypt(user_password, owner_password, use_128bit=True)
            
            # Add security metadata
            writer.add_metadata({
                '/Title': f'{thesis_title} - SELF-DESTRUCT PROTECTED',
                '/Author': author_name,
                '/Subject': 'CONFIDENTIEL - SELF-DESTRUCT ENABLED - 3 ATTEMPTS MAX',
                '/Creator': 'Advanced PDF Security with Self-Destruct',
                '/Producer': f'Protected by {author_name} - PERMANENT DESTRUCTION AFTER 3 FAILED ATTEMPTS',
                '/CreationDate': f"D:{datetime.now().strftime('%Y%m%d%H%M%S')}",
                '/Keywords': 'SELF-DESTRUCT, CONFIDENTIEL, 3-ATTEMPTS-MAX, PERMANENT-LOCKOUT',
                '/SecurityLevel': 'MAXIMUM - SELF-DESTRUCT ENABLED',
                '/WarningLevel': 'CRITICAL - NO RECOVERY AFTER FAILURE'
            })
            
            # Write protected PDF
            with open(output_pdf, 'wb') as output_file:
                writer.write(output_file)
        
        print("✅ Protected PDF created successfully!")
        print()
        
        # Create instruction files
        create_user_instructions(output_pdf, user_password, owner_password)
        create_email_template(output_pdf, user_password)
        create_viewer_script(output_pdf, author_name)
        
        print("🎉 PROTECTION COMPLETE!")
        print("=" * 40)
        print(f"📁 Protected file: {output_pdf}")
        print(f"🔑 User password: {user_password}")
        print(f"🔐 Owner password: {owner_password}")
        print()
        print("⚠️  CRITICAL WARNING:")
        print("• Self-destruct enabled after 3 failed attempts")
        print("• NO RECOVERY possible after destruction")
        print("• Document becomes PERMANENTLY inaccessible")
        print("• Use with extreme caution!")
        print()
        print("📋 Files created:")
        print(f"• {output_pdf} (Protected PDF)")
        print(f"• {output_pdf.replace('.pdf', '_INSTRUCTIONS.txt')} (User guide)")
        print(f"• {output_pdf.replace('.pdf', '_EMAIL.txt')} (Email template)")
        print(f"• {output_pdf.replace('.pdf', '_VIEWER.py')} (Self-destruct viewer)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating protected PDF: {e}")
        return False

def create_user_instructions(output_pdf, user_password, owner_password):
    """Create detailed user instructions"""
    instruction_file = output_pdf.replace('.pdf', '_INSTRUCTIONS.txt')
    
    with open(instruction_file, 'w', encoding='utf-8') as f:
        f.write("=" * 70 + "\n")
        f.write("🔒 SELF-DESTRUCT PROTECTED PDF - CRITICAL INSTRUCTIONS\n")
        f.write("=" * 70 + "\n\n")
        
        f.write("⚠️  DANGER: SELF-DESTRUCT ENABLED ⚠️\n")
        f.write("This document will PERMANENTLY self-destruct after 3 failed password attempts!\n\n")
        
        f.write(f"📄 Protected Document: {os.path.basename(output_pdf)}\n")
        f.write(f"👨‍🎓 Author: Abdelhalim Serhani\n")
        f.write(f"🏫 Université Hassan II Casablanca\n")
        f.write(f"📅 Protected: {datetime.now().strftime('%d/%m/%Y à %H:%M')}\n\n")
        
        f.write("🔑 AUTHENTICATION INFORMATION:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Password for opening/printing: {user_password}\n\n")
        
        f.write("💥 SELF-DESTRUCT WARNING:\n")
        f.write("-" * 25 + "\n")
        f.write("• ATTEMPT 1: Warning - 2 attempts remaining\n")
        f.write("• ATTEMPT 2: Final warning - 1 attempt remaining\n")
        f.write("• ATTEMPT 3: PERMANENT DESTRUCTION - NO RECOVERY\n\n")
        
        f.write("✅ AUTHORIZED ACTIONS:\n")
        f.write("• Open document with correct password\n")
        f.write("• Print document (all pages)\n")
        f.write("• View content on screen\n\n")
        
        f.write("❌ PROHIBITED ACTIONS:\n")
        f.write("• Copy text from document\n")
        f.write("• Modify document content\n")
        f.write("• Save under different name\n")
        f.write("• Share electronically\n")
        f.write("• Redistribute file\n\n")
        
        f.write("🚨 SECURITY PROTOCOL:\n")
        f.write("• Failed attempts are tracked permanently\n")
        f.write("• Security logs are created automatically\n")
        f.write("• Breach reports generated on desktop\n")
        f.write("• NO RECOVERY MECHANISM EXISTS\n\n")
        
        f.write("📞 EMERGENCY CONTACT:\n")
        f.write("If you believe this is an error, contact:\n")
        f.write("Abdelhalim Serhani - Document Author\n")
        f.write("IMMEDIATELY after any failed attempts\n\n")
        
        f.write("⚖️  LEGAL WARNING:\n")
        f.write("Violation of these restrictions constitutes copyright\n")
        f.write("infringement and may result in legal action.\n\n")
        
        f.write("=" * 70 + "\n")
        f.write("🔐 DOCUMENT OWNER SECTION (CONFIDENTIAL)\n")
        f.write("=" * 70 + "\n")
        f.write(f"Master password: {owner_password}\n")
        f.write("⚠️  KEEP THIS SECRET - PROVIDES FULL CONTROL\n")

def create_email_template(output_pdf, user_password):
    """Create email template for sending protected document"""
    email_file = output_pdf.replace('.pdf', '_EMAIL.txt')
    
    with open(email_file, 'w', encoding='utf-8') as f:
        f.write("EMAIL TEMPLATE FOR SELF-DESTRUCT PROTECTED DOCUMENT\n")
        f.write("=" * 55 + "\n\n")
        
        f.write("Subject: ⚠️  SELF-DESTRUCT PROTECTED THESIS - HANDLE WITH EXTREME CARE\n\n")
        
        f.write("URGENT - PLEASE READ CAREFULLY BEFORE OPENING\n")
        f.write("=" * 45 + "\n\n")
        
        f.write("Bonjour,\n\n")
        
        f.write("Je vous envoie ma thèse sous forme de document PDF avec\n")
        f.write("PROTECTION SELF-DESTRUCT intégrée. ATTENTION CRITIQUE:\n\n")
        
        f.write("🚨 AVERTISSEMENT SELF-DESTRUCT:\n")
        f.write(f"• Le fichier se DÉTRUIRA DÉFINITIVEMENT après 3 tentatives échouées\n")
        f.write("• AUCUNE RÉCUPÉRATION n'est possible après destruction\n")
        f.write("• Mot de passe: {user_password}\n")
        f.write("• Tapez le mot de passe EXACTEMENT comme écrit\n\n")
        
        f.write("📋 INSTRUCTIONS CRITIQUES:\n")
        f.write("1. Utilisez le mot de passe fourni EXACTEMENT\n")
        f.write("2. Si erreur = avertissement + tentatives restantes\n")
        f.write("3. 3ème erreur = DESTRUCTION PERMANENTE\n")
        f.write("4. Imprimez immédiatement après ouverture\n")
        f.write("5. SUPPRIMEZ le fichier après impression\n\n")
        
        f.write("⚠️  EN CAS D'ERREUR:\n")
        f.write("Si vous tapez mal le mot de passe, ARRÊTEZ IMMÉDIATEMENT\n")
        f.write("et contactez-moi avant de réessayer!\n\n")
        
        f.write("Le document fait environ 150 pages. Impression recto-verso\n")
        f.write("recommandée pour économiser le papier.\n\n")
        
        f.write("MERCI DE CONFIRMER LA RÉCEPTION DE CE MESSAGE.\n\n")
        
        f.write("Cordialement,\n")
        f.write("Abdelhalim Serhani\n")
        f.write("Étudiant Master - Université Hassan II Casablanca\n\n")
        
        f.write("P.S: Ce système de sécurité est IRRÉVERSIBLE.\n")
        f.write("En cas de doute, contactez-moi AVANT d'ouvrir le fichier.\n")

def create_viewer_script(output_pdf, author_name):
    """Create a standalone viewer script with self-destruct"""
    viewer_file = output_pdf.replace('.pdf', '_VIEWER.py')
    
    with open(viewer_file, 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('"""\n')
        f.write('Self-Destruct PDF Viewer\n')
        f.write(f'Protected document: {os.path.basename(output_pdf)}\n')
        f.write(f'Author: {author_name}\n')
        f.write(f'Created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
        f.write('\n')
        f.write('⚠️  WARNING: SELF-DESTRUCT ENABLED\n')
        f.write('This viewer will permanently destroy document access after 3 failed attempts!\n')
        f.write('"""\n\n')
        
        f.write('import os\n')
        f.write('import sys\n\n')
        
        f.write('def main():\n')
        f.write('    print("🔒 Self-Destruct PDF Viewer")\n')
        f.write('    print("=" * 40)\n')
        f.write(f'    print("Document: {os.path.basename(output_pdf)}")\n')
        f.write(f'    print("Author: {author_name}")\n')
        f.write('    print()\n')
        f.write('    print("⚠️  CRITICAL WARNING:")\n')
        f.write('    print("• Self-destruct enabled after 3 failed attempts")\n')
        f.write('    print("• NO RECOVERY possible after destruction")\n')
        f.write('    print("• Use correct password on first try!")\n')
        f.write('    print()\n')
        f.write('    \n')
        f.write('    # To use this viewer, you need the advanced_pdf_security.py module\n')
        f.write('    try:\n')
        f.write('        from advanced_pdf_security import SecurePDFViewer\n')
        f.write('        \n')
        f.write('        config = {\n')
        f.write(f'            "author": "{author_name}",\n')
        f.write(f'            "title": "{os.path.splitext(os.path.basename(output_pdf))[0]}",\n')
        f.write('            "max_attempts": 3,\n')
        f.write('            "lockout_hours": 24,\n')
        f.write('            "auto_destruct": True,\n')
        f.write('            "log_attempts": True\n')
        f.write('        }\n')
        f.write('        \n')
        f.write(f'        viewer = SecurePDFViewer("{output_pdf}", config)\n')
        f.write('        viewer.open_document()\n')
        f.write('        \n')
        f.write('    except ImportError:\n')
        f.write('        print("❌ Error: advanced_pdf_security.py module not found!")\n')
        f.write('        print("Please ensure advanced_pdf_security.py is in the same directory.")\n')
        f.write('        sys.exit(1)\n')
        f.write('    except Exception as e:\n')
        f.write('        print(f"❌ Error: {e}")\n')
        f.write('        sys.exit(1)\n\n')
        
        f.write('if __name__ == "__main__":\n')
        f.write('    main()\n')

def main():
    """Main function"""
    print("🚀 Starting PDF Protection Process...")
    print()
    
    success = create_protected_copy()
    
    if success:
        print()
        print("🎯 NEXT STEPS:")
        print("1. Test the protected PDF with correct password first")
        print("2. Send the protected PDF + instructions to print shop")
        print("3. Keep the owner password safe for emergencies")
        print("4. Monitor security logs if needed")
        print()
        print("🛡️  Your thesis is now protected with military-grade security!")
    else:
        print("❌ Protection process failed. Please check the error messages above.")

if __name__ == "__main__":
    main() 