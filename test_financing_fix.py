#!/usr/bin/env python3
"""
Test script to validate the financing structure comparison fix
"""
import ast

def test_financing_structure_fix():
    """Test that the financing structure data preparation now returns the correct format"""
    
    report_service_path = "/home/<USER>/Documents/repos/flet/Hiel-RnE-Model-v4/services/report_service.py"
    
    try:
        with open(report_service_path, 'r') as f:
            content = f.read()
        
        # Parse the file to check for syntax errors
        ast.parse(content)
        print("✓ report_service.py has valid Python syntax")
        
        # Check for the specific patterns we need
        required_patterns = [
            "'lcoe': baseline_lcoe",
            "'irr_project': baseline_irr",
            "'total_cost': assumptions.capex_meur",
            "baseline_kpis = analysis_results.get('financial', {}).get('kpis', {})",
            "baseline_lcoe = baseline_kpis.get('LCOE_eur_kwh', 0.045) * 1000",
            "baseline_irr = baseline_kpis.get('IRR_project', 0.12)"
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print("✗ Some required patterns are missing:")
            for pattern in missing_patterns:
                print(f"  - Missing: {pattern}")
            return False
        else:
            print("✓ All required patterns found in financing structure data preparation")
            
        # Check that old problematic patterns are gone
        old_patterns = [
            "'Total Cost (M€)': assumptions.capex_meur",
            "'Grant Funding (M€)': 0.0",
            "'Debt (M€)': assumptions.capex_meur * assumptions.debt_ratio",
            "'Equity (M€)': assumptions.capex_meur * (1 - assumptions.debt_ratio)",
            "'Weighted Cost (%)': 6.5"
        ]
        
        remaining_old_patterns = []
        for pattern in old_patterns:
            if pattern in content:
                remaining_old_patterns.append(pattern)
        
        if remaining_old_patterns:
            print("✗ Some old patterns still remain:")
            for pattern in remaining_old_patterns:
                print(f"  - Found: {pattern}")
            return False
        else:
            print("✓ Old problematic patterns have been removed")
            
        print("\n📊 Expected Data Structure:")
        print("  - 'lcoe': LCOE values in c€/kWh for chart comparison")
        print("  - 'irr_project': Project IRR values for chart comparison")
        print("  - 'total_cost': Total project costs for chart comparison")
        print("  - Additional financing details preserved for completeness")
        
        print("\n🎯 Expected Chart Behavior:")
        print("  - Traditional Financing: Worst performance (highest LCOE, lowest IRR)")
        print("  - Morocco Only: Moderate improvement")
        print("  - Piano Mattei Only: Good improvement")
        print("  - Cross-Financing: Best performance (lowest LCOE, highest IRR)")
        
        return True
        
    except SyntaxError as e:
        print(f"✗ Syntax error in report_service.py: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading report_service.py: {e}")
        return False

if __name__ == "__main__":
    success = test_financing_structure_fix()
    if success:
        print("\n🎉 Financing structure comparison fix has been successfully applied!")
        print("The charts should now display meaningful data comparing different financing scenarios.")
    else:
        print("\n❌ Some issues remain that need to be addressed.")