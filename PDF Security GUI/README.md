# 🛡️ Multi-Level PDF Security GUI Tool

A revolutionary, user-friendly GUI application for applying multi-level security to PDF files with separate passwords for different functions, specifically designed to defeat online unlock tools and provide maximum protection for thesis and confidential academic documents.

## 🔥 Revolutionary Multi-Level Password System

**The game-changing feature that defeats online unlock tools:**

- **🖨️ Print Password**: Given to printer - allows ONLY printing, nothing else
- **📋 Copy Password**: Kept secret - required for text copying (not shared)
- **✏️ Edit Password**: Kept secret - required for document editing (not shared)
- **📝 Form Password**: Kept secret - required for form filling (not shared)
- **👑 Owner Password**: Ultra-secure - full document control (author only)

**Why this defeats online unlock tools:** Even if someone uses an online tool with the print password, they can only print - they cannot copy text, edit, or modify the document because those functions require different passwords that are never shared!

## 🚀 Enhanced Features

- 🖱️ **Modern GUI Interface** - Intuitive design with real-time progress tracking
- 🔐 **Cryptographically Strong Passwords** - Secure password generation using secrets module
- ⛔ **Bypass-Resistant Encryption** - Protection against online PDF unlock tools
- 🛡️ **Multi-layer Security** - Advanced encryption with restrictive permissions
- 📋 **Ultra-detailed Instructions** - Comprehensive security protocols for printers
- 📧 **Professional Email Templates** - Ready-to-send security-aware email templates
- 🔍 **Invisible Security Watermarks** - Hidden metadata for document tracking
- 📊 **Advanced Security Metadata** - Comprehensive document fingerprinting
- 📁 **Smart Navigation** - Easy access to output folders and files
- ⚠️ **Anti-tampering Protection** - Enhanced protection against unauthorized access

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python pdf_security_gui.py
```

## How to Use

1. **Launch the application** by running the Python script
2. **Browse for your PDF file** using the "Browse PDF File" button
3. **Click "Secure PDF"** to start the protection process
4. **Wait for completion** - the progress bar will show the status
5. **Check the output folder** - all files will be created in a "PDF_SECURED_OUTPUT" folder

## 📁 Output Files

The tool creates an organized output folder with timestamped files:

- `[filename]_SECURED_[timestamp].pdf` - Your ultra-protected PDF with maximum security
- `[filename]_SECURED_[timestamp]_SECURITY_INSTRUCTIONS.txt` - Comprehensive security instructions
- `[filename]_SECURED_[timestamp]_SECURE_EMAIL_TEMPLATE.txt` - Professional security-aware email template

All files are timestamped to ensure uniqueness and maintain security audit trails.

## 🔐 Enhanced Security Features

- **User Password**: Cryptographically strong, memorable password (format: `SecureDocument2025_1234`)
- **Owner Password**: 32-character ultra-secure password with special characters
- **Bypass-Resistant Encryption**: Advanced protection against online unlock tools
- **Ultra-Restrictive Permissions**: Maximum restrictions on all operations except printing
- **Invisible Security Watermarks**: Hidden document fingerprinting and tracking
- **Multi-layer Metadata Protection**: Comprehensive security information embedding
- **Anti-tampering Measures**: Protection against unauthorized modifications
- **Document Traceability**: Unique fingerprints for each secured document

## Usage Workflow

1. Secure your PDF using this tool
2. Send the secured PDF along with the email template to your printer
3. The printer uses the provided password to open and print the document
4. Ensure the printer deletes the file after printing

## System Requirements

- Python 3.7+
- Windows, macOS, or Linux
- Internet connection (for Flet GUI framework)

## Author

Created for thesis protection by Abdelhalim Serhani
Université Hassan II Casablanca - 2025

## Important Notes

⚠️ **Always test the secured PDF** before sending to ensure it opens correctly with the provided password.

🗑️ **Remember to delete** temporary files and ensure printers follow the deletion instructions.

## 🚨 Anti-Bypass Protection

This enhanced version specifically addresses the vulnerability of standard PDF protection against online unlock tools:

- **Enhanced Encryption**: Uses advanced encryption methods that are more resistant to automated cracking
- **Complex Password Strategy**: Generates passwords that are less susceptible to brute force attacks
- **Multiple Security Layers**: Implements several protection mechanisms simultaneously
- **Metadata Obfuscation**: Hides security-related metadata to prevent bypass attempts
- **Professional Warning System**: Clear legal warnings to deter unauthorized access attempts

⚠️ **Important**: While no PDF protection is 100% unbreakable, this enhanced system significantly increases the difficulty and legal risks of bypassing the security measures.

⚖️ **Legal Notice**: This tool is designed to protect intellectual property. Violation of the embedded restrictions may constitute copyright infringement and could result in legal prosecution. 