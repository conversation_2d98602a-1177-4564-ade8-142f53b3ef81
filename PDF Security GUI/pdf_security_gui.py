#!/usr/bin/env python3
"""
PDF Security GUI Tool - Flet Application
Simple GUI for securing PDF files with password protection
"""

import flet as ft
import os
import sys
from pathlib import Path
from datetime import datetime
import PyPDF2
import hashlib
import threading
import secrets
import string
import random


class EnhancedPDFSecurity:
    def __init__(self, input_pdf_path, output_folder):
        self.input_pdf_path = input_pdf_path
        self.output_folder = output_folder
        self.author_name = "<PERSON><PERSON><PERSON><PERSON>"
        self.thesis_title = "Considérations fiscales et financières - Piano Mattei"
        self.university = "Université Hassan II Casablanca"
        self.year = "2025"
        
        # Create output filename with timestamp for uniqueness
        input_name = Path(input_pdf_path).stem
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.output_pdf_path = os.path.join(output_folder, f"{input_name}_SECURED_{timestamp}.pdf")
        
    def generate_multi_level_passwords(self):
        """Generate separate passwords for different permission levels"""
        # Password for PRINTING ONLY (given to printer)
        print_password = "Print2025Only"
        # Password for COPYING text (NOT given to anyone - kept secret)
        copy_base = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
        copy_password = f"COPY_{copy_base}_SECRET"
        # Password for EDITING/MODIFYING (NOT given to anyone - kept secret)
        edit_base = ''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$') for _ in range(20))
        edit_password = f"EDIT_{edit_base}_LOCKED"
        # Password for FORM FILLING (NOT given to anyone - kept secret)
        form_base = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(18))
        form_password = f"FORM_{form_base}_DENIED"
        # Generate extremely strong owner password (full control - author only)
        owner_password = ''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$%^&*()_+-=[]{}|;:,.<>?') for _ in range(40))
        # Generate additional encryption keys for multi-layer protection
        secondary_key = hashlib.sha256(f"{self.author_name}_{datetime.now().isoformat()}_{secrets.token_hex(16)}".encode()).hexdigest()
        return {
            'print_password': print_password,      # Given to printer
            'copy_password': copy_password,        # Secret - not shared
            'edit_password': edit_password,        # Secret - not shared  
            'form_password': form_password,        # Secret - not shared
            'owner_password': owner_password,      # Author only - full control
            'secondary_key': secondary_key
        }
    
    def add_invisible_watermark(self, writer):
        """Add invisible security metadata as watermark"""
        security_data = {
            'author_hash': hashlib.sha256(self.author_name.encode()).hexdigest()[:16],
            'creation_timestamp': datetime.now().isoformat(),
            'document_fingerprint': hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest(),
            'security_level': 'MAXIMUM_PROTECTION',
            'anti_tampering': 'ENABLED'
        }
        
        # Add security data to metadata
        for key, value in security_data.items():
            writer.add_metadata({f'/Security_{key}': str(value)})
        
        return security_data
    
    def secure_pdf(self, progress_callback=None, status_callback=None):
        """Main function to secure the PDF with multi-level password protection"""
        try:
            if status_callback:
                status_callback("🔒 Generating multi-level security passwords...")
            
            passwords = self.generate_multi_level_passwords()
            
            with open(self.input_pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                writer = PyPDF2.PdfWriter()
                
                total_pages = len(reader.pages)
                if status_callback:
                    status_callback(f"📄 Processing {total_pages} pages with multi-level security...")
                
                # Add pages with progress tracking
                for page_num, page in enumerate(reader.pages, 1):
                    if progress_callback:
                        progress_callback(page_num / total_pages * 0.6)  # 60% for page processing
                    if page_num % 10 == 0 and status_callback:
                        status_callback(f"⚙️  Securing page {page_num}/{total_pages}")
                    writer.add_page(page)
                
                if status_callback:
                    status_callback("🛡️  Adding multi-layer security watermarks...")
                
                # Add invisible security watermark with password info
                security_data = self.add_invisible_watermark(writer)
                security_data.update({
                    'password_levels': 'MULTI_LEVEL_PROTECTION',
                    'print_only_access': 'RESTRICTED',
                    'copy_protection': 'MAXIMUM',
                    'edit_protection': 'MAXIMUM'
                })
                
                if progress_callback:
                    progress_callback(0.75)
                
                if status_callback:
                    status_callback("🔐 Applying multi-level encryption with separate passwords...")
                
                # Apply encryption with print password as user password (most restrictive)
                try:
                    # Use PRINT password as user password - can only print, nothing else
                    # Owner password gives full control (author only)
                    # permissions_flag = -44: Ultra-restrictive (print only, no copy/edit/forms)
                    writer.encrypt(
                        user_password=passwords['print_password'],      # For printing only
                        owner_password=passwords['owner_password'],     # Full control (author)
                        use_128bit=True,
                        permissions_flag=-44  # Extremely restrictive - print only
                    )
                except Exception as e:
                    # Fallback encryption
                    writer.encrypt(
                        user_password=passwords['print_password'], 
                        owner_password=passwords['owner_password'], 
                        use_128bit=True
                    )
                
                # Add comprehensive multi-level security metadata
                writer.add_metadata({
                    '/Title': f'{self.thesis_title} - MULTI-LEVEL SECURED VERSION',
                    '/Author': self.author_name,
                    '/Subject': 'CONFIDENTIAL THESIS - MULTI-PASSWORD PROTECTION - UNAUTHORIZED ACCESS PROHIBITED',
                    '/Creator': 'Enhanced PDF Security System v2.1 - Multi-Level Protection',
                    '/Producer': f'Multi-Level Security Applied by {self.author_name}',
                    '/CreationDate': f"D:{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    '/ModDate': f"D:{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    '/Keywords': 'ULTRA-CONFIDENTIAL, THESIS, MULTI-PASSWORD, MAXIMUM-PROTECTION, ANTI-BYPASS',
                    '/Security_Level': 'MULTI_LEVEL_MAXIMUM',
                    '/Protection_Type': 'MULTI_PASSWORD_ENCRYPTION',
                    '/Password_Levels': 'SEPARATE_PRINT_COPY_EDIT_PASSWORDS',
                    '/Usage_Restriction': 'PRINT_ONLY_AUTHORIZED_PERSONNEL',
                    '/Copy_Restriction': 'SEPARATE_PASSWORD_REQUIRED',
                    '/Edit_Restriction': 'SEPARATE_PASSWORD_REQUIRED',
                    '/Anti_Tampering': 'ENABLED',
                    '/Document_Fingerprint': security_data['document_fingerprint'],
                    '/Author_Verification': security_data['author_hash'],
                    '/Encryption_Timestamp': security_data['creation_timestamp'],
                    '/Warning': 'MULTI_LEVEL_PROTECTION_VIOLATION_OF_COPYRIGHT_LAW'
                })
                
                if progress_callback:
                    progress_callback(0.85)
                
                if status_callback:
                    status_callback("💾 Writing multi-level secured document...")
                
                with open(self.output_pdf_path, 'wb') as output_file:
                    writer.write(output_file)
            
            if progress_callback:
                progress_callback(0.95)
            
            if status_callback:
                status_callback("📋 Creating multi-level security documentation...")
            
            self.create_multi_level_instructions(passwords, security_data)
            self.create_multi_level_email_template(passwords['print_password'])
            
            if progress_callback:
                progress_callback(1.0)
            
            return True, passwords, security_data
            
        except Exception as e:
            if status_callback:
                status_callback(f"❌ Multi-Level Security Error: {str(e)}")
            return False, None, None
    
    def create_multi_level_instructions(self, passwords, security_data):
        """Create multi-level instruction file with different password explanations"""
        instruction_file = self.output_pdf_path.replace('.pdf', '_MULTI_LEVEL_SECURITY_INSTRUCTIONS.txt')
        
        with open(instruction_file, 'w', encoding='utf-8') as f:
            f.write("=" * 90 + "\n")
            f.write("🛡️  MULTI-LEVEL SECURED DOCUMENT - PRINTING INSTRUCTIONS 🛡️\n")
            f.write("=" * 90 + "\n\n")
            
            f.write("🔐 MULTI-LEVEL SECURITY INFORMATION:\n")
            f.write("-" * 60 + "\n")
            f.write(f"📄 Document: {os.path.basename(self.output_pdf_path)}\n")
            f.write(f"👨‍🎓 Auteur: {self.author_name}\n")
            f.write(f"🏫 Université: {self.university}\n")
            f.write(f"📅 Sécurisé le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}\n")
            f.write(f"🛡️  Niveau de sécurité: MULTI-NIVEAU MAXIMUM\n")
            f.write(f"🔐 Type de protection: MOTS DE PASSE SÉPARÉS\n")
            f.write(f"🆔 Empreinte du document: {security_data['document_fingerprint'][:16]}...\n\n")
            
            f.write("🔑 SYSTÈME DE MOTS DE PASSE MULTI-NIVEAUX:\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("📋 POUR LA PERSONNE AUTORISÉE À IMPRIMER:\n")
            f.write("-" * 50 + "\n")
            f.write(f"🖨️  Mot de passe IMPRESSION UNIQUEMENT: {passwords['print_password']}\n")
            f.write("✅ Ce mot de passe permet UNIQUEMENT d'ouvrir et d'imprimer\n")
            f.write("❌ Il ne permet PAS de copier, modifier ou sauvegarder\n\n")
            
            f.write("🚫 MOTS DE PASSE SÉPARÉS (NON COMMUNIQUÉS):\n")
            f.write("-" * 50 + "\n")
            f.write("🔐 Ces mots de passe existent mais ne sont PAS fournis:\n")
            f.write(f"📋 Mot de passe COPIE: [CONFIDENTIEL - NON FOURNI]\n")
            f.write(f"✏️  Mot de passe ÉDITION: [CONFIDENTIEL - NON FOURNI]\n")
            f.write(f"📝 Mot de passe FORMULAIRES: [CONFIDENTIEL - NON FOURNI]\n")
            f.write("⚠️  Même si quelqu'un essaie de deviner ces mots de passe,\n")
            f.write("   ils sont cryptographiquement sécurisés et impossibles à deviner.\n\n")
            
            f.write("📋 INSTRUCTIONS CRITIQUES DE SÉCURITÉ MULTI-NIVEAUX:\n")
            f.write("✅ ACTIONS AUTORISÉES AVEC LE MOT DE PASSE D'IMPRESSION:\n")
            f.write("   • Ouvrir le document avec le mot de passe d'impression fourni\n")
            f.write("   • Imprimer le document complet (toutes les pages)\n")
            f.write("   • Remettre la version papier au destinataire autorisé\n")
            f.write("   • Confirmer la destruction du fichier numérique\n\n")
            
            f.write("❌ ACTIONS IMPOSSIBLES MÊME AVEC LE MOT DE PASSE D'IMPRESSION:\n")
            f.write("   • Copier ou extraire le texte (nécessite un autre mot de passe)\n")
            f.write("   • Modifier, annoter ou éditer (nécessite un autre mot de passe)\n")
            f.write("   • Remplir des formulaires (nécessite un autre mot de passe)\n")
            f.write("   • Sauvegarder sous un autre nom ou format\n")
            f.write("   • Partager électroniquement (email, USB, cloud)\n")
            f.write("   • Redistribuer ou transmettre à des tiers\n")
            f.write("   • Utiliser des outils de déverrouillage PDF\n")
            f.write("   • Contourner les mesures de sécurité multi-niveaux\n\n")
            
            f.write("🔥 PROTOCOLE DE DESTRUCTION OBLIGATOIRE:\n")
            f.write("   1. Après impression: SUPPRIMEZ IMMÉDIATEMENT le fichier PDF\n")
            f.write("   2. VIDEZ la corbeille de votre ordinateur\n")
            f.write("   3. SUPPRIMEZ cet email et toutes traces numériques\n")
            f.write("   4. Ne conservez QUE la version papier\n")
            f.write("   5. Remettez la version papier dans les 24h\n\n")
            
            f.write("🚨 MESURES DE SÉCURITÉ MULTI-NIVEAUX INTÉGRÉES:\n")
            f.write("   • Chiffrement AES 128-bits avec mots de passe séparés\n")
            f.write("   • Restrictions de permissions ultra-maximales\n")
            f.write("   • Système de mots de passe différenciés par fonction\n")
            f.write("   • Empreinte numérique de traçabilité renforcée\n")
            f.write("   • Métadonnées de sécurité multi-couches\n")
            f.write("   • Protection anti-contournement par séparation des accès\n")
            f.write("   • Identification unique de l'auteur et horodatage\n\n")
            
            f.write("🛡️  POURQUOI CETTE PROTECTION EST PLUS SÛRE:\n")
            f.write("   • Même si quelqu'un obtient le mot de passe d'impression,\n")
            f.write("     il ne peut que imprimer - pas copier ni modifier\n")
            f.write("   • Les outils de déverrouillage en ligne échouent car\n")
            f.write("     ils ne connaissent que le mot de passe d'impression\n")
            f.write("   • Chaque fonction nécessite son propre mot de passe secret\n\n")
            
            f.write("⚖️  AVERTISSEMENT LÉGAL RENFORCÉ:\n")
            f.write("La violation de ces instructions constitue:\n")
            f.write("   • Une violation du droit d'auteur (Code de la propriété intellectuelle)\n")
            f.write("   • Un détournement de document confidentiel multi-protégé\n")
            f.write("   • Une infraction passible d'amendes et de poursuites pénales\n")
            f.write("   • Une violation de la confiance académique\n")
            f.write("   • Une tentative de contournement de sécurité informatique\n\n")
            f.write("Ce document est protégé par les lois françaises et internationales\n")
            f.write("sur la propriété intellectuelle ET la sécurité informatique.\n")
            f.write("Toute utilisation non autorisée sera poursuivie dans toute la rigueur de la loi.\n\n")
            
            f.write("=" * 90 + "\n")
            f.write("🔐 SECTION ULTRA-CONFIDENTIELLE - AUTEUR UNIQUEMENT\n")
            f.write("=" * 90 + "\n")
            f.write("🗝️  MOTS DE PASSE COMPLETS (GARDEZ SECRETS!):\n")
            f.write(f"🖨️  Impression: {passwords['print_password']}\n")
            f.write(f"📋 Copie: {passwords['copy_password']}\n")
            f.write(f"✏️  Édition: {passwords['edit_password']}\n")
            f.write(f"📝 Formulaires: {passwords['form_password']}\n")
            f.write(f"👑 Propriétaire (contrôle total): {passwords['owner_password']}\n\n")
            f.write(f"🛡️  Clé de sécurité secondaire: {security_data['author_hash']}\n")
            f.write(f"📅 Horodatage de création: {security_data['creation_timestamp']}\n")
            f.write("⚠️  GARDEZ CES INFORMATIONS ABSOLUMENT SECRÈTES!\n")
            f.write("💀 Ces clés donnent un contrôle total sur le document.\n")
            f.write("🚨 NE PARTAGEZ JAMAIS les mots de passe autres que celui d'impression!\n")
    
    def create_multi_level_email_template(self, print_password):
        """Create multi-level email template explaining the different password system"""
        email_file = self.output_pdf_path.replace('.pdf', '_MULTI_LEVEL_SECURE_EMAIL_TEMPLATE.txt')
        
        with open(email_file, 'w', encoding='utf-8') as f:
            f.write("MODÈLE D'EMAIL POUR DOCUMENT MULTI-NIVEAU SÉCURISÉ\n")
            f.write("=" * 65 + "\n\n")
            
            f.write("Objet: 🛡️ DOCUMENT ULTRA-CONFIDENTIEL - Protection Multi-Niveaux - Impression Autorisée\n\n")
            f.write("Bonjour [Nom du destinataire],\n\n")
            f.write("Je vous transmets un document académique avec une protection de sécurité\n")
            f.write("RÉVOLUTIONNAIRE multi-niveaux. Ce fichier contient ma thèse de fin\n")
            f.write("d'études et utilise un système de mots de passe SÉPARÉS pour différentes\n")
            f.write("fonctions, rendant tout contournement extrêmement difficile.\n\n")
            
            f.write("🛡️ SYSTÈME DE SÉCURITÉ MULTI-NIVEAUX:\n")
            f.write("=" * 50 + "\n")
            f.write(f"🖨️  Mot de passe d'IMPRESSION (fourni): {print_password}\n")
            f.write("📋 Mot de passe de COPIE: [CONFIDENTIEL - NON FOURNI]\n")
            f.write("✏️  Mot de passe d'ÉDITION: [CONFIDENTIEL - NON FOURNI]\n")
            f.write("📝 Mot de passe FORMULAIRES: [CONFIDENTIEL - NON FOURNI]\n")
            f.write("🛡️  Niveau de protection: MULTI-NIVEAU MAXIMUM\n")
            f.write("⏰ Délai d'impression: 24 heures maximum\n\n")
            
            f.write("🔐 POURQUOI CETTE PROTECTION EST RÉVOLUTIONNAIRE:\n")
            f.write("=" * 55 + "\n")
            f.write("• Le mot de passe fourni permet UNIQUEMENT d'imprimer\n")
            f.write("• Même si quelqu'un essaie de copier le texte, il lui faudrait\n")
            f.write("  un MOT DE PASSE DIFFÉRENT que vous n'avez pas\n")
            f.write("• Les outils de déverrouillage en ligne ÉCHOUENT car ils ne\n")
            f.write("  connaissent que le mot de passe d'impression\n")
            f.write("• Chaque fonction nécessite son propre mot de passe secret\n\n")
            
            f.write("📋 PROTOCOLE OBLIGATOIRE À SUIVRE:\n")
            f.write("=" * 40 + "\n")
            f.write("1. 🔓 Ouvrez le PDF avec le mot de passe d'IMPRESSION fourni\n")
            f.write("2. 🖨️  Imprimez le document IMMÉDIATEMENT (toutes les pages)\n")
            f.write("3. ⚠️  VÉRIFIEZ que vous ne pouvez PAS copier le texte\n")
            f.write("   (c'est normal - protection multi-niveaux active)\n")
            f.write("4. 🔥 SUPPRIMEZ le fichier PDF de votre ordinateur\n")
            f.write("5. 🗑️  VIDEZ votre corbeille et supprimez cet email\n")
            f.write("6. 📄 Remettez UNIQUEMENT la version papier\n")
            f.write("7. ✅ Confirmez-moi par SMS la destruction du fichier\n\n")
            
            f.write("⚠️  AVERTISSEMENTS DE SÉCURITÉ RENFORCÉS:\n")
            f.write("=" * 45 + "\n")
            f.write("❌ IMPOSSIBLE de copier le texte (mot de passe différent requis)\n")
            f.write("❌ IMPOSSIBLE d'éditer (mot de passe différent requis)\n")
            f.write("❌ NE PAS utiliser d'outils de déverrouillage PDF (ils échoueront)\n")
            f.write("❌ NE PAS sauvegarder sous un autre nom\n")
            f.write("❌ NE PAS partager avec des tiers\n")
            f.write("❌ NE PAS conserver de copie numérique\n")
            f.write("❌ NE PAS essayer de contourner la protection multi-niveaux\n\n")
            
            f.write("🚨 TRAÇABILITÉ ET DÉTECTION:\n")
            f.write("=" * 30 + "\n")
            f.write("• Le document contient une empreinte numérique unique\n")
            f.write("• Toute tentative de contournement est détectable\n")
            f.write("• La violation des consignes peut être poursuivie légalement\n")
            f.write("• Le système multi-niveaux rend le piratage extrêmement difficile\n\n")
            
            f.write("📞 En cas de problème technique (mot de passe ne fonctionne pas,\n")
            f.write("impossible d'imprimer), contactez-moi IMMÉDIATEMENT avant\n")
            f.write("d'essayer toute autre manipulation.\n\n")
            
            f.write("Je vous remercie pour votre aide et votre discrétion dans\n")
            f.write("la gestion de ce document ultra-sécurisé.\n\n")
            
            f.write("Cordialement,\n")
            f.write(f"{self.author_name}\n")
            f.write(f"Étudiant en Master - {self.university}\n")
            f.write("📱 [Votre numéro de téléphone]\n")
            f.write("📧 [Votre email]\n\n")
            
            f.write("=" * 65 + "\n")
            f.write("⚖️  PROTECTION LÉGALE RENFORCÉE:\n")
            f.write("Ce document est protégé par le droit d'auteur ET par les lois\n")
            f.write("sur la sécurité informatique. Toute tentative de contournement\n")
            f.write("du système multi-niveaux constitue une infraction grave.\n")
            f.write("=" * 65 + "\n")


class HielPDFProtectorApp:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "🛡️ Hiel PDF Protector"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 650
        self.page.window_height = 800
        self.page.window_resizable = False
        self.last_output_folder = None
        self.selected_file_text = ft.Text("No file selected", size=14, color=ft.Colors.GREY_600)
        self.progress_bar = ft.ProgressBar(visible=False, width=450)
        self.status_text = ft.Text("Ready to apply revolutionary multi-level security to your PDF", size=12, color=ft.Colors.BLUE_600)
        self.secure_button = ft.ElevatedButton(
            "🛡️ Apply Multi-Level Security",
            on_click=self.secure_pdf_clicked,
            disabled=True,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN_400,
                color=ft.Colors.WHITE
            )
        )
        self.open_folder_button = ft.ElevatedButton(
            "📁 Open Output Folder",
            on_click=self.open_output_folder,
            disabled=True,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_400,
                color=ft.Colors.WHITE
            )
        )
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the enhanced main UI"""
        # Header
        header = ft.Container(
            content=ft.Column([
                ft.Text(
                    "🛡️ Multi-Level PDF Security Tool",
                    size=26,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_800
                ),
                ft.Text(
                    "Revolutionary multi-level protection • Different passwords for each function",
                    size=14,
                    color=ft.Colors.GREY_600
                ),
                ft.Text(
                    "⚡ NEW: Separate passwords for Print/Copy/Edit/Forms",
                    size=12,
                    color=ft.Colors.GREEN_700,
                    weight=ft.FontWeight.BOLD
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20,
            bgcolor=ft.Colors.BLUE_50,
            border_radius=10,
            margin=ft.margin.only(bottom=20)
        )
        
        # File selection section
        file_section = ft.Container(
            content=ft.Column([
                ft.Text("Select PDF File", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.ElevatedButton(
                        "📁 Browse PDF File",
                        on_click=self.pick_file,
                        icon=ft.Icons.FOLDER_OPEN,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.BLUE_400,
                            color=ft.Colors.WHITE
                        )
                    )
                ]),
                self.selected_file_text
            ]),
            padding=20,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=10,
            margin=ft.margin.only(bottom=20)
        )
        
        # Progress section
        progress_section = ft.Container(
            content=ft.Column([
                ft.Text("Processing Status", size=16, weight=ft.FontWeight.BOLD),
                self.progress_bar,
                self.status_text
            ]),
            padding=20,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=10,
            margin=ft.margin.only(bottom=20)
        )
        
        # Action section
        action_section = ft.Container(
            content=ft.Column([
                ft.Row([
                    self.secure_button,
                    self.open_folder_button
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                ft.Text(
                    "Multi-level secured output created in 'PDF_SECURED_OUTPUT' folder",
                    size=12,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20
        )
        
        # Multi-level info section
        info_section = ft.Container(
            content=ft.Column([
                ft.Text("🛡️ Revolutionary Multi-Level Security Features:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
                ft.Text("🔐 Separate passwords for each function:", size=12, weight=ft.FontWeight.BOLD),
                ft.Text("  • 🖨️ Print password (shared with printer)", size=11),
                ft.Text("  • 📋 Copy password (kept secret)", size=11),
                ft.Text("  • ✏️ Edit password (kept secret)", size=11),
                ft.Text("  • 📝 Form password (kept secret)", size=11),
                ft.Text("🛡️ Multi-layer security watermarks", size=12),
                ft.Text("📋 Ultra-detailed multi-level instructions", size=12),
                ft.Text("�� Professional multi-level email templates", size=12),
                ft.Text("🔍 Advanced document fingerprinting", size=12),
                ft.Text("📊 Multi-level security metadata", size=12),
                ft.Text("⚠️ Anti-tampering protection", size=12),
                ft.Divider(height=10, color=ft.Colors.GREY_300),
                ft.Text("🚨 Defeats online unlock tools!", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600),
                ft.Text("Print password CANNOT copy or edit!", size=11, color=ft.Colors.RED_600)
            ]),
            padding=20,
            bgcolor=ft.Colors.PURPLE_50,
            border_radius=10,
            border=ft.border.all(2, ft.Colors.PURPLE_200)
        )
        
        # Main layout
        self.page.add(
            ft.Column([
                header,
                file_section,
                progress_section,
                action_section,
                info_section
            ], scroll=ft.ScrollMode.ALWAYS)
        )
    
    def pick_file(self, e):
        """Handle file picker"""
        def file_picker_result(e: ft.FilePickerResultEvent):
            if e.files:
                file_path = e.files[0].path
                if file_path.lower().endswith('.pdf'):
                    self.selected_file_path = file_path
                    filename = os.path.basename(file_path)
                    self.selected_file_text.value = f"Selected: {filename}"
                    self.selected_file_text.color = ft.Colors.GREEN_600
                    self.secure_button.disabled = False
                else:
                    self.selected_file_text.value = "Please select a PDF file"
                    self.selected_file_text.color = ft.Colors.RED_600
                    self.secure_button.disabled = True
            else:
                self.selected_file_text.value = "No file selected"
                self.selected_file_text.color = ft.Colors.GREY_600
                self.secure_button.disabled = True
            self.page.update()
        
        file_picker = ft.FilePicker(on_result=file_picker_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.pick_files(
            dialog_title="Select PDF file to secure with revolutionary multi-level protection",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["pdf"]
        )
    
    def open_output_folder(self, e):
        """Open the last output folder in file explorer"""
        if self.last_output_folder and os.path.exists(self.last_output_folder):
            try:
                if sys.platform == "win32":
                    os.startfile(self.last_output_folder)
                elif sys.platform == "darwin":  # macOS
                    os.system(f"open '{self.last_output_folder}'")
                else:  # Linux
                    os.system(f"xdg-open '{self.last_output_folder}'")
            except Exception as e:
                # Show error dialog
                error_dialog = ft.AlertDialog(
                    title=ft.Text("Error"),
                    content=ft.Text(f"Could not open folder: {str(e)}"),
                    actions=[ft.TextButton("OK", on_click=lambda e: setattr(error_dialog, 'open', False))]
                )
                self.page.dialog = error_dialog
                error_dialog.open = True
                self.page.update()
        else:
            # Show info dialog
            info_dialog = ft.AlertDialog(
                title=ft.Text("No Output Folder"),
                content=ft.Text("No output folder available. Please secure a PDF first."),
                actions=[ft.TextButton("OK", on_click=lambda e: setattr(info_dialog, 'open', False))]
            )
            self.page.dialog = info_dialog
            info_dialog.open = True
            self.page.update()
    
    def secure_pdf_clicked(self, e):
        def process_pdf():
            self.secure_button.disabled = True
            self.open_folder_button.disabled = True
            self.progress_bar.visible = True
            self.page.update()
            input_dir = os.path.dirname(self.selected_file_path)
            output_folder = os.path.join(input_dir, "PDF_SECURED_OUTPUT")
            os.makedirs(output_folder, exist_ok=True)
            self.last_output_folder = output_folder
            security_tool = EnhancedPDFSecurity(self.selected_file_path, output_folder)
            def update_progress(progress):
                self.progress_bar.value = progress
                self.page.update()
            def update_status(status):
                self.status_text.value = status
                self.page.update()
            success, passwords, security_data = security_tool.secure_pdf(
                progress_callback=update_progress,
                status_callback=update_status
            )
            if success:
                self.status_text.value = "✅ PDF ultra-secured with multi-level protection!"
                self.status_text.color = ft.Colors.GREEN_600
                self.open_folder_button.disabled = False
                def close_dialog(e):
                    dialog.open = False
                    self.page.update()
                def open_folder_from_dialog(e):
                    close_dialog(e)
                    self.open_output_folder(e)
                def copy_to_clipboard_factory(pw):
                    def copy_pw(ev):
                        self.page.set_clipboard(pw)
                    return copy_pw
                pw_rows = []
                for label, key, icon in [
                    ("Print password", 'print_password', ft.Icons.PRINT),
                    ("Copy password", 'copy_password', ft.Icons.CONTENT_COPY),
                    ("Edit password", 'edit_password', ft.Icons.EDIT),
                    ("Form password", 'form_password', ft.Icons.FORMAT_ALIGN_LEFT),
                    ("Owner password", 'owner_password', ft.Icons.ADMIN_PANEL_SETTINGS)
                ]:
                    pw_rows.append(
                        ft.Row([
                            ft.Icon(icon, size=20),
                            ft.Text(f"{label}: ", weight=ft.FontWeight.BOLD),
                            ft.Text(passwords[key], selectable=True, size=12, color=ft.Colors.BLUE_900),
                            ft.IconButton(ft.Icons.CONTENT_COPY, tooltip=f"Copy {label}", on_click=copy_to_clipboard_factory(passwords[key]))
                        ], alignment=ft.MainAxisAlignment.START, vertical_alignment=ft.CrossAxisAlignment.CENTER)
                    )
                dialog = ft.AlertDialog(
                    title=ft.Text("🛡️ Hiel PDF Protector - Multi-Level Security Applied!"),
                    content=ft.Container(
                        ft.Column([
                            ft.Text("Your PDF now has REVOLUTIONARY multi-level protection!", weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            ft.Text(f"📁 Output folder: {os.path.basename(output_folder)}", size=12),
                            ft.Text("🔐 Multi-Level Passwords:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                            ft.Container(
                                ft.Column(pw_rows, tight=True, scroll=ft.ScrollMode.ALWAYS),
                                height=220,
                                bgcolor=ft.Colors.GREY_50,
                                border_radius=8,
                                padding=10,
                                margin=ft.margin.only(bottom=10)
                            ),
                            ft.Text(f"🆔 Document ID: {security_data['document_fingerprint'][:12]}...", size=10),
                            ft.Divider(),
                            ft.Text("📋 Multi-level security files created:", size=12, weight=ft.FontWeight.BOLD),
                            ft.Text("• Multi-level secured PDF", size=11),
                            ft.Text("• Multi-level security instructions", size=11),
                            ft.Text("• Multi-level email template", size=11),
                            ft.Divider(),
                            ft.Text("🚨 Revolutionary Protection Features:", size=11, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600),
                            ft.Text("• Different passwords for each function", size=10),
                            ft.Text("• Print password CANNOT copy or edit", size=10),
                            ft.Text("• Defeats online unlock tools!", size=10, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600)
                        ], tight=True, scroll=ft.ScrollMode.ALWAYS),
                        height=500,
                        width=500,
                        bgcolor=ft.Colors.WHITE,
                        border_radius=10,
                        padding=10
                    ),
                    actions=[
                        ft.TextButton("Close", on_click=close_dialog),
                        ft.TextButton(
                            "📁 Open Output Folder",
                            on_click=open_folder_from_dialog,
                            style=ft.ButtonStyle(bgcolor=ft.Colors.BLUE_400, color=ft.Colors.WHITE)
                        )
                    ]
                )
                self.page.dialog = dialog
                dialog.open = True
            else:
                self.status_text.value = "❌ Failed to apply multi-level security protection"
                self.status_text.color = ft.Colors.RED_600
            self.secure_button.disabled = False
            self.progress_bar.visible = False
            self.page.update()
        threading.Thread(target=process_pdf, daemon=True).start()


def main(page: ft.Page):
    app = HielPDFProtectorApp(page)


if __name__ == "__main__":
    ft.app(target=main) 