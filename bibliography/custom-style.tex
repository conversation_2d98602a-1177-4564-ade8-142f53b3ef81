% Custom bibliography styling
\usepackage{etoolbox}
\usepackage{xcolor}

% Define colors for bibliography
\definecolor{bibcolor}{RGB}{0, 51, 102}  % Dark blue color for headings
\definecolor{citecolor}{RGB}{31, 73, 125}  % Slightly lighter blue for citations

% Add styling to the bibliography heading 
\patchcmd{\thebibliography}
  {\section*{\refname}}
  {\section*{\color{bibcolor}\refname}}
  {}{}

% Format the bibliography labels
\renewcommand{\@biblabel}[1]{{\color{citecolor}[#1]}}

% Add a subtle background to the bibliography section
\let\oldbibliography\bibliography
\renewcommand{\bibliography}[1]{%
  \begingroup
  \setlength{\fboxsep}{5pt}%
  \setlength{\leftmargin}{2em}%
  \setlength{\itemindent}{-2em}%
  \oldbibliography{#1}%
  \endgroup
}

% Make authors' names stand out
\AtBeginBibliography{\renewcommand*{\mkbibnamefamily}[1]{\textbf{#1}}}

% Use a nice font for the bibliography if available
\IfFileExists{times.sty}{\RequirePackage{times}}{}

% Customize the formatting of different types of entries
\newcommand{\formatbook}[1]{\textit{#1}}
\newcommand{\formatjournal}[1]{\textit{#1}}
\newcommand{\formatarticle}[1]{``#1''} 