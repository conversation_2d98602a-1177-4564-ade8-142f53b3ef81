#!/usr/bin/env python3
"""
Simple PDF Security Tool for Thesis Protection
Creates a secure version of a PDF with:
- Password protection (print-only permissions)
- Protective metadata
- Clear instructions for the printer
"""

import sys
import os
from datetime import datetime
import PyPDF2
import hashlib

class SimplePDFSecurity:
    def __init__(self, input_pdf_path, output_pdf_path):
        self.input_pdf_path = input_pdf_path
        self.output_pdf_path = output_pdf_path
        self.author_name = "<PERSON><PERSON><PERSON><PERSON>"
        self.thesis_title = "Considérations fiscales et financières - Piano Mattei"
        self.university = "Université Hassan II Casablanca"
        self.year = "2025"
        
    def generate_passwords(self):
        """Generate secure passwords for the PDF"""
        # User password (for the person who will print) - simple and memorable
        user_password = f"Print{self.year}Only"
        
        # Owner password (for you - full control) - more complex
        base_string = f"{self.author_name}_{self.thesis_title}_{datetime.now().strftime('%Y%m%d')}"
        owner_password = hashlib.md5(base_string.encode()).hexdigest()[:16]
        
        return user_password, owner_password
    
    def secure_pdf(self):
        """Main function to secure the PDF"""
        try:
            print(f"🔒 Securing PDF: {self.input_pdf_path}")
            
            # Generate passwords
            user_password, owner_password = self.generate_passwords()
            
            # Read original PDF
            with open(self.input_pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                writer = PyPDF2.PdfWriter()
                
                total_pages = len(reader.pages)
                print(f"📄 Processing {total_pages} pages...")
                
                # Copy all pages
                for page_num, page in enumerate(reader.pages, 1):
                    if page_num % 10 == 0:
                        print(f"⚙️  Processing page {page_num}/{total_pages}")
                    writer.add_page(page)
                
                # Set PDF security with strict permissions
                try:
                    writer.encrypt(
                        user_password=user_password,
                        owner_password=owner_password,
                        use_128bit=True,
                        permissions_flag=4  # Print only permission
                    )
                except Exception as e:
                    print(f"⚠️  Using alternative encryption method: {e}")
                    writer.encrypt(user_password, owner_password, use_128bit=True)
                
                # Add protective metadata
                writer.add_metadata({
                    '/Title': f'{self.thesis_title} - Version Protégée pour Impression',
                    '/Author': self.author_name,
                    '/Subject': 'Mémoire de Master - CONFIDENTIEL - Impression uniquement',
                    '/Creator': 'PDF Security Tool - Version Sécurisée',
                    '/Producer': f'Document protégé par {self.author_name}',
                    '/CreationDate': f"D:{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    '/Keywords': 'CONFIDENTIEL, Thèse, Piano Mattei, Impression Uniquement, Protégé',
                    '/CustomField1': 'REPRODUCTION INTERDITE',
                    '/CustomField2': f'Document ID: {datetime.now().strftime("%Y%m%d%H%M%S")}'
                })
                
                # Write secured PDF
                with open(self.output_pdf_path, 'wb') as output_file:
                    writer.write(output_file)
            
            print(f"✅ PDF secured successfully!")
            print(f"📁 Output file: {self.output_pdf_path}")
            print(f"👤 User password (for printing): {user_password}")
            print(f"🔑 Owner password (full control): {owner_password}")
            
            # Create instruction files
            self.create_instructions(user_password, owner_password)
            self.create_email_template(user_password)
            
            return True
            
        except Exception as e:
            print(f"❌ Error securing PDF: {str(e)}")
            return False
    
    def create_instructions(self, user_password, owner_password):
        """Create an instruction file for the person who will print"""
        instruction_file = self.output_pdf_path.replace('.pdf', '_INSTRUCTIONS.txt')
        
        with open(instruction_file, 'w', encoding='utf-8') as f:
            f.write("=" * 70 + "\n")
            f.write("🔒 INSTRUCTIONS POUR L'IMPRESSION DU DOCUMENT SÉCURISÉ 🔒\n")
            f.write("=" * 70 + "\n\n")
            
            f.write(f"📄 Document: {os.path.basename(self.output_pdf_path)}\n")
            f.write(f"👨‍🎓 Auteur: {self.author_name}\n")
            f.write(f"🏫 Université: {self.university}\n")
            f.write(f"📅 Sécurisé le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}\n\n")
            
            f.write("🔑 POUR LA PERSONNE QUI VA IMPRIMER:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Mot de passe d'ouverture: {user_password}\n\n")
            
            f.write("📋 INSTRUCTIONS IMPORTANTES:\n")
            f.write("✅ Ce que vous POUVEZ faire:\n")
            f.write("   • Ouvrir le document avec le mot de passe\n")
            f.write("   • Imprimer le document (toutes les pages)\n")
            f.write("   • Remettre la version papier au professeur\n\n")
            
            f.write("❌ Ce que vous NE POUVEZ PAS faire:\n")
            f.write("   • Copier le texte du document\n")
            f.write("   • Modifier le contenu\n")
            f.write("   • Sauvegarder sous un autre nom\n")
            f.write("   • Partager électroniquement\n")
            f.write("   • Redistribuer le fichier\n\n")
            
            f.write("🗑️  APRÈS L'IMPRESSION:\n")
            f.write("   • SUPPRIMEZ ce fichier PDF de votre ordinateur\n")
            f.write("   • SUPPRIMEZ cet email/message\n")
            f.write("   • Remettez UNIQUEMENT la version papier\n\n")
            
            f.write("🛡️  SÉCURITÉS INTÉGRÉES:\n")
            f.write("   • Protection par mot de passe\n")
            f.write("   • Permissions limitées (impression uniquement)\n")
            f.write("   • Métadonnées de traçabilité\n")
            f.write("   • Identification de l'auteur\n\n")
            
            f.write("⚖️  AVERTISSEMENT LÉGAL:\n")
            f.write("La violation de ces restrictions constitue une violation du\n")
            f.write("droit d'auteur et peut entraîner des poursuites légales.\n")
            f.write("Ce document est la propriété intellectuelle de l'auteur.\n\n")
            
            f.write("=" * 70 + "\n")
            f.write("🔐 SECTION CONFIDENTIELLE - AUTEUR UNIQUEMENT\n")
            f.write("=" * 70 + "\n")
            f.write(f"Mot de passe propriétaire: {owner_password}\n")
            f.write("⚠️  GARDE CE MOT DE PASSE SECRET - il donne un contrôle total!\n")
        
        print(f"📋 Instructions créées: {instruction_file}")
    
    def create_email_template(self, user_password):
        """Create an email template for sending the document"""
        email_file = self.output_pdf_path.replace('.pdf', '_EMAIL_TEMPLATE.txt')
        
        with open(email_file, 'w', encoding='utf-8') as f:
            f.write("MODÈLE D'EMAIL POUR ENVOYER LE DOCUMENT SÉCURISÉ\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("Objet: Document sécurisé pour impression - Thèse PFE\n\n")
            
            f.write("Bonjour,\n\n")
            
            f.write("Je vous envoie ci-joint ma thèse de fin d'études sous forme\n")
            f.write("de document PDF sécurisé. Mon professeur a demandé une version\n")
            f.write("papier et j'aurais besoin de votre aide pour l'imprimer.\n\n")
            
            f.write("INFORMATIONS IMPORTANTES:\n")
            f.write("• Le fichier est protégé par mot de passe\n")
            f.write(f"• Mot de passe: {user_password}\n")
            f.write("• Vous pouvez uniquement l'imprimer (pas de copie/modification)\n")
            f.write("• Merci de supprimer le fichier après impression\n")
            f.write("• Remettre uniquement la version papier\n\n")
            
            f.write("Le document contient environ 150 pages. Merci de l'imprimer\n")
            f.write("recto-verso si possible pour économiser le papier.\n\n")
            
            f.write("Je vous remercie énormément pour votre aide.\n\n")
            
            f.write("Cordialement,\n")
            f.write(f"{self.author_name}\n")
            f.write(f"Étudiant en Master - {self.university}\n\n")
            
            f.write("P.S: Ce document est confidentiel et ne doit pas être\n")
            f.write("partagé avec d'autres personnes.\n")
        
        print(f"📧 Modèle d'email créé: {email_file}")

def main():
    if len(sys.argv) != 3:
        print("Usage: python simple_secure_pdf.py <input_pdf> <output_pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not os.path.exists(input_pdf):
        print(f"❌ Le fichier d'entrée n'existe pas: {input_pdf}")
        sys.exit(1)
    
    # Create security tool and secure the PDF
    security_tool = SimplePDFSecurity(input_pdf, output_pdf)
    success = security_tool.secure_pdf()
    
    if success:
        print("\n🎉 Votre thèse est maintenant protégée!")
        print("📤 Vous pouvez l'envoyer en toute sécurité pour impression.")
        print("📋 Consultez les fichiers d'instructions créés.")
    else:
        print("\n❌ Échec de la sécurisation du PDF.")
        sys.exit(1)

if __name__ == "__main__":
    main() 