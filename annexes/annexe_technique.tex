\chapter{Annexe Technique : Hiel RnE Modeler v3.0}
\label{annexe:hiel-model}

\section{Présentation générale du logiciel}

\subsection{Informations générales}

Le \textbf{Hiel RnE Financial Model v3.0} est une solution logicielle professionnelle de modélisation financière spécialement conçue pour l'analyse de projets d'énergie solaire photovoltaïque. Développé par Abdelhalim Serhani pour Agevolami SRL, ce logiciel permet aux investisseurs, développeurs de projets, consultants financiers et institutions bancaires d'évaluer la viabilité économique et financière de projets solaires dans différents pays, notamment le Maroc et l'Italie.

\textbf{Slogan :} "Your way to explore crossborder opportunities and grow big"

\subsection{Architecture technique détaillée}

\subsubsection{Choix technologiques et justifications}

Le développement du Hiel RnE Modeler v3.0 s'appuie sur un ensemble de technologies modernes, choisies pour leur fiabilité dans le domaine de la modélisation financière :

\begin{itemize}
    \item \textbf{Python 3.8+} : Langage principal, choisi pour sa richesse en bibliothèques scientifiques
    \item \textbf{Flet Framework} : Interface utilisateur multi-plateforme moderne
    \item \textbf{SQLite} : Base de données embarquée pour le stockage sécurisé
    \item \textbf{Matplotlib + Plotly} : Génération de graphiques 2D et 3D interactifs
    \item \textbf{OpenPyXL} : Export Excel avec formatage avancé
    \item \textbf{Python-DOCX} : Génération de rapports Word automatisés
\end{itemize}



\section{Fonctionnalités principales}

\subsection{Modélisation financière avancée}

Le Hiel RnE Modeler v3.0 offre des capacités de modélisation financière sophistiquées :

\begin{itemize}
    \item \textbf{Analyse DCF (Discounted Cash Flow)} : Calculs de VAN, TRI, et période de retour
    \item \textbf{Modélisation de la dette} : Structures de financement complexes avec plusieurs tranches
    \item \textbf{Analyse de sensibilité} : Tests de robustesse sur les paramètres clés
    \item \textbf{Simulations Monte-Carlo} : Évaluation des risques par méthodes stochastiques
    \item \textbf{Optimisation LCOE} : Calcul et optimisation du coût actualisé de l'énergie
\end{itemize}

\subsection{Gestion des incitations et subventions}

Le logiciel intègre une base de données complète des mécanismes de soutien :

\begin{itemize}
    \item \textbf{Maroc} : Subventions MASEN, IRESEN, et avantages fiscaux
    \item \textbf{Italie} : Piano Mattei, crédits d'impôt, et mécanismes SIMEST
    \item \textbf{Optimisation transfrontalière} : Combinaison optimale des incitations
\end{itemize}

\subsection{Interface utilisateur et expérience}

\begin{itemize}
    \item \textbf{Interface intuitive} : Design moderne avec navigation simplifiée
    \item \textbf{Tableaux de bord interactifs} : Visualisation en temps réel des KPI
    \item \textbf{Graphiques dynamiques} : Charts 2D/3D avec zoom et export

\end{itemize}



\section{Fonctionnalités spécialisées}

\subsection{Modélisation transfrontalière avancée}

Le Hiel RnE Modeler v3.0 intègre des fonctionnalités uniques pour l'analyse de projets transfrontaliers :

\begin{itemize}
    \item \textbf{Optimisation des incitations croisées} : Combinaison optimale des subventions italiennes (Piano Mattei) et marocaines (MASEN/IRESEN)
    \item \textbf{Financement SIMEST intégré} : Modélisation des prêts bonifiés, garanties SACE, et fonds SIMEST Africa
    \item \textbf{Optimisation fiscale internationale} : Calculs d'optimisation des structures fiscales transfrontalières
    \item \textbf{Gestion des devises} : Prise en compte des risques de change EUR/MAD
    \item \textbf{Conformité réglementaire} : Respect des cadres juridiques italien et marocain
\end{itemize}

\subsection{Modules d'analyse avancée}

\begin{itemize}
    \item \textbf{Analyse de scénarios} : Comparaison de multiples configurations de financement
    \item \textbf{Optimisation de la structure de capital} : Ratio dette/fonds propres optimal
    \item \textbf{Analyse de risque intégrée} : Évaluation des risques techniques, financiers, et réglementaires
    \item \textbf{Modélisation de la dégradation} : Prise en compte de la dégradation des panneaux dans le temps
\end{itemize}

\subsection{Modélisation des incitations croisées}

Le modèle intègre automatiquement les dispositifs d'incitation suivants :

\textbf{Côté italien :}
\begin{itemize}
    \item Subventions Piano Mattei (jusqu'à 10\% du CAPEX)
    \item Prêts bonifiés SIMEST (taux préférentiel 0,371\%)
    \item Garanties SACE (jusqu'à 80\% du financement)
    \item Fonds SIMEST Afrique (conditions spéciales)
\end{itemize}

\textbf{Côté marocain :}
\begin{itemize}
    \item Subventions MASEN (10\% CAPEX zones prioritaires)
    \item Fonds innovation IRESEN (5\% CAPEX projets innovants)
    \item Primes Charte d'Investissement (jusqu'à 30\% cumulées)
    \item Exonérations fiscales (TVA, IS pendant 5 ans)
\end{itemize}

\subsection{Profils de localisation pré-configurés}

Le modèle inclut des profils détaillés pour les principales zones d'investissement :

\begin{table}[H]
    \centering
    \caption{Paramètres techniques par localisation}
    \begin{tabular}{|l|c|c|c|}
    \hline
    \textbf{Paramètre} & \textbf{Ouarzazate} & \textbf{Dakhla} & \textbf{Noor Midelt} \\
    \hline
    Irradiation (kWh/m²/an) & 2,400 & 2,560 & 2,350 \\
    Facteur de charge (\%) & 22.8 & 24.1 & 22.3 \\
    CAPEX majoré logistique & Base & +3\% & +1.5\% \\
    OPEX majoré logistique & Base & +8\% & +4\% \\
    \hline
    \end{tabular}
\end{table}

\section{Validation et benchmarking}

\subsection{Méthodologie de validation}

La fiabilité du modèle a été validée selon plusieurs approches :

\begin{enumerate}
    \item \textbf{Validation croisée Excel} : Comparaison systématique avec modèles de référence
    \item \textbf{Benchmarking sectoriel} : Confrontation avec données IRENA et projets réels
    \item \textbf{Tests de cohérence} : Vérification équilibre comptable (bilan/CR/flux)
    \item \textbf{Peer review} : Validation par experts en financement de projets
\end{enumerate}

\subsection{Tests de robustesse}

Le modèle a été testé sur différents scénarios :
\begin{itemize}
    \item Projets de 5 à 50 MW
    \item Différentes structures de financement (30\% à 80\% dette)
    \item Variations de taux d'intérêt (1\% à 6\%)
    \item Scénarios de stress (CAPEX +20\%, production -10\%)
\end{itemize}


\section{Perspectives d'évolution}

\subsection{Développements futurs}

Le modèle pourrait être étendu pour inclure :
\begin{itemize}
    \item Support multi-technologies (éolien, hybride, stockage)
    \item Intégration API données météo temps réel
    \item Module optimisation portefeuille multi-projets
    \item Interface web collaborative pour équipes distribuées
\end{itemize}

