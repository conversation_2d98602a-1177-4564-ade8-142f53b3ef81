#!/usr/bin/env python3
"""
Universal PDF Security Tool
Can be applied to any PDF file with customizable protection settings.
Usage:
    python universal_pdf_security.py file.pdf                    # Basic protection
    python universal_pdf_security.py file1.pdf file2.pdf         # Multiple files
    python universal_pdf_security.py *.pdf                       # All PDFs in folder
    python universal_pdf_security.py --batch folder/             # All PDFs in a folder
"""

import sys
import os
import glob
from datetime import datetime
import PyPDF2
import hashlib
import argparse

class UniversalPDFSecurity:
    def __init__(self, author_name="Document Author", document_type="Document"):
        self.author_name = author_name
        self.document_type = document_type
        self.year = datetime.now().year
        
    def generate_passwords(self, filename):
        """Generate secure passwords for the PDF"""
        # Extract filename without extension for password
        base_name = os.path.splitext(os.path.basename(filename))[0]
        
        # User password (for printing) - simple and memorable
        user_password = f"Print{self.year}_{base_name[:8]}"
        
        # Owner password (for full control) - more complex
        base_string = f"{self.author_name}_{base_name}_{datetime.now().strftime('%Y%m%d')}"
        owner_password = hashlib.md5(base_string.encode()).hexdigest()[:16]
        
        return user_password, owner_password
    
    def secure_pdf(self, input_pdf_path, custom_settings=None):
        """Secure a single PDF file"""
        try:
            # Generate output filename
            base_name = os.path.splitext(input_pdf_path)[0]
            output_pdf_path = f"{base_name}_SECURE.pdf"
            
            print(f"🔒 Securing: {os.path.basename(input_pdf_path)}")
            
            # Generate passwords
            user_password, owner_password = self.generate_passwords(input_pdf_path)
            
            # Read original PDF
            with open(input_pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                writer = PyPDF2.PdfWriter()
                
                total_pages = len(reader.pages)
                print(f"   📄 {total_pages} pages")
                
                # Copy all pages
                for page in reader.pages:
                    writer.add_page(page)
                
                # Apply security settings
                permissions = 4  # Print only by default
                if custom_settings:
                    permissions = custom_settings.get('permissions', 4)
                
                try:
                    writer.encrypt(
                        user_password=user_password,
                        owner_password=owner_password,
                        use_128bit=True,
                        permissions_flag=permissions
                    )
                except Exception as e:
                    print(f"   ⚠️  Using alternative encryption: {e}")
                    writer.encrypt(user_password, owner_password, use_128bit=True)
                
                # Add protective metadata
                writer.add_metadata({
                    '/Title': f'{self.document_type} - Version Protégée',
                    '/Author': self.author_name,
                    '/Subject': f'{self.document_type} - CONFIDENTIEL - Impression uniquement',
                    '/Creator': 'Universal PDF Security Tool',
                    '/Producer': f'Sécurisé par {self.author_name}',
                    '/CreationDate': f"D:{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    '/Keywords': 'CONFIDENTIEL, Protégé, Impression Uniquement',
                    '/SecurityLevel': 'HIGH',
                    '/DocumentID': f'{datetime.now().strftime("%Y%m%d%H%M%S")}'
                })
                
                # Write secured PDF
                with open(output_pdf_path, 'wb') as output_file:
                    writer.write(output_file)
            
            print(f"   ✅ Created: {os.path.basename(output_pdf_path)}")
            print(f"   🔑 Password: {user_password}")
            
            # Create instruction file
            self.create_instructions(output_pdf_path, user_password, owner_password)
            
            return {
                'success': True,
                'output_file': output_pdf_path,
                'user_password': user_password,
                'owner_password': owner_password
            }
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_instructions(self, output_pdf_path, user_password, owner_password):
        """Create instruction file for each secured PDF"""
        instruction_file = output_pdf_path.replace('.pdf', '_INSTRUCTIONS.txt')
        
        with open(instruction_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("🔒 DOCUMENT SÉCURISÉ - INSTRUCTIONS\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"📄 Fichier: {os.path.basename(output_pdf_path)}\n")
            f.write(f"👤 Auteur: {self.author_name}\n")
            f.write(f"📅 Sécurisé le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}\n\n")
            
            f.write("🔑 MOT DE PASSE POUR OUVERTURE:\n")
            f.write(f"Password: {user_password}\n\n")
            
            f.write("✅ AUTORISÉ:\n")
            f.write("• Ouvrir avec le mot de passe\n")
            f.write("• Imprimer le document\n")
            f.write("• Visualiser le contenu\n\n")
            
            f.write("❌ INTERDIT:\n")
            f.write("• Copier le texte\n")
            f.write("• Modifier le contenu\n")
            f.write("• Sauvegarder sous un autre nom\n")
            f.write("• Redistribuer électroniquement\n\n")
            
            f.write("⚠️  IMPORTANT:\n")
            f.write("Ce document est protégé par le droit d'auteur.\n")
            f.write("La violation de ces restrictions peut entraîner\n")
            f.write("des poursuites légales.\n\n")
            
            f.write("=" * 60 + "\n")
            f.write("🔐 PROPRIÉTAIRE UNIQUEMENT\n")
            f.write("=" * 60 + "\n")
            f.write(f"Mot de passe maître: {owner_password}\n")
            f.write("⚠️  GARDER SECRET!\n")
    
    def batch_secure(self, file_patterns):
        """Secure multiple PDF files"""
        results = []
        
        for pattern in file_patterns:
            if os.path.isdir(pattern):
                # If it's a directory, find all PDFs in it
                pdf_files = glob.glob(os.path.join(pattern, "*.pdf"))
            else:
                # If it's a pattern or file, expand it
                pdf_files = glob.glob(pattern)
            
            for pdf_file in pdf_files:
                if os.path.isfile(pdf_file) and pdf_file.lower().endswith('.pdf'):
                    # Skip already secured files
                    if '_SECURE' not in pdf_file:
                        result = self.secure_pdf(pdf_file)
                        results.append(result)
                    else:
                        print(f"⏭️  Skipping already secured: {os.path.basename(pdf_file)}")
        
        return results

def main():
    parser = argparse.ArgumentParser(
        description="Universal PDF Security Tool - Secure any PDF with password protection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python universal_pdf_security.py document.pdf
  python universal_pdf_security.py file1.pdf file2.pdf file3.pdf
  python universal_pdf_security.py *.pdf
  python universal_pdf_security.py --batch documents/
  python universal_pdf_security.py --author "John Doe" --type "Report" document.pdf
        """
    )
    
    parser.add_argument('files', nargs='+', help='PDF file(s) or pattern(s) to secure')
    parser.add_argument('--author', '-a', default='Document Author', 
                       help='Author name for the documents')
    parser.add_argument('--type', '-t', default='Document', 
                       help='Document type (e.g., "Thesis", "Report", "Contract")')
    parser.add_argument('--batch', '-b', action='store_true',
                       help='Treat arguments as directories to process')
    
    args = parser.parse_args()
    
    # Create security tool with custom settings
    security_tool = UniversalPDFSecurity(
        author_name=args.author,
        document_type=args.type
    )
    
    print("🛡️  Universal PDF Security Tool")
    print("=" * 50)
    print(f"👤 Author: {args.author}")
    print(f"📋 Document Type: {args.type}")
    print("=" * 50)
    
    # Process files
    if args.batch:
        # Batch mode - treat arguments as directories
        results = security_tool.batch_secure(args.files)
    else:
        # Individual files mode
        results = []
        for file_path in args.files:
            if '*' in file_path or '?' in file_path:
                # Handle wildcards
                expanded_files = glob.glob(file_path)
                for expanded_file in expanded_files:
                    if expanded_file.lower().endswith('.pdf') and '_SECURE' not in expanded_file:
                        result = security_tool.secure_pdf(expanded_file)
                        results.append(result)
            else:
                # Single file
                if os.path.exists(file_path) and file_path.lower().endswith('.pdf'):
                    result = security_tool.secure_pdf(file_path)
                    results.append(result)
                else:
                    print(f"❌ File not found or not a PDF: {file_path}")
    
    # Summary
    successful = len([r for r in results if r.get('success', False)])
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"📊 SUMMARY: {successful}/{total} files secured successfully")
    
    if successful > 0:
        print("\n🎉 Files are now protected!")
        print("📋 Check the _INSTRUCTIONS.txt files for usage details")
        print("🔑 Passwords are unique for each file")
    
    return 0 if successful == total else 1

if __name__ == "__main__":
    sys.exit(main()) 