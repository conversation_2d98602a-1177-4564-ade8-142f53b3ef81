import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.ticker as mtick
import numpy as np

# --- Définir le nom du fichier Excel et les noms des feuilles ---
# IMPORTANT : Assurez-vous que c'est le nom correct de votre fichier Excel.
EXCEL_FILE = "solar_financial_model.xlsx"

# IMPORTANT : Mettez à jour ces noms de feuilles s'ils sont différents dans votre fichier Excel.
# Exemples possibles en français : SHEET_INPUTS = "Entrées" ou "Hypothèses"
SHEET_INPUTS = "Inputs"
SHEET_CASHFLOW = "Cashflow"
SHEET_KPIS = "KPIs" # Ou "Indicateurs"
SHEET_SENSITIVITY = "Sensitivity" # Ou "Sensibilité"

# --- Charger les données depuis les feuilles Excel ---
print(f"--- Tentative de chargement du fichier Excel : {EXCEL_FILE} ---")
try:
    xls = pd.ExcelFile(EXCEL_FILE)
    print(f"Fichier Excel '{EXCEL_FILE}' trouvé. Feuilles disponibles : {xls.sheet_names}")

    if SHEET_INPUTS in xls.sheet_names:
        inputs_df = pd.read_excel(xls, sheet_name=SHEET_INPUTS)
        print(f"\n--- Feuille '{SHEET_INPUTS}' chargée ---")
        print(inputs_df.head())
    else:
        print(f"AVERTISSEMENT : Feuille '{SHEET_INPUTS}' non trouvée.")
        inputs_df = pd.DataFrame() # Créer un DataFrame vide pour éviter les erreurs plus tard

    if SHEET_CASHFLOW in xls.sheet_names:
        cashflow_df = pd.read_excel(xls, sheet_name=SHEET_CASHFLOW)
        print(f"\n--- Feuille '{SHEET_CASHFLOW}' chargée ---")
        print(cashflow_df.head())
    else:
        print(f"ERREUR : Feuille '{SHEET_CASHFLOW}' non trouvée. De nombreux graphiques en dépendent.")
        cashflow_df = pd.DataFrame()

    if SHEET_KPIS in xls.sheet_names:
        kpis_df = pd.read_excel(xls, sheet_name=SHEET_KPIS)
        print(f"\n--- Feuille '{SHEET_KPIS}' chargée ---")
        print(kpis_df.head())
    else:
        print(f"AVERTISSEMENT : Feuille '{SHEET_KPIS}' non trouvée.")
        kpis_df = pd.DataFrame()

    if SHEET_SENSITIVITY in xls.sheet_names:
        sensitivity_df = pd.read_excel(xls, sheet_name=SHEET_SENSITIVITY)
        print(f"\n--- Feuille '{SHEET_SENSITIVITY}' chargée ---")
        print(sensitivity_df.head())
    else:
        print(f"AVERTISSEMENT : Feuille '{SHEET_SENSITIVITY}' non trouvée.")
        sensitivity_df = pd.DataFrame()

except FileNotFoundError:
    print(f"ERREUR : Le fichier Excel '{EXCEL_FILE}' n'a pas été trouvé.")
    print("Veuillez vous assurer que le fichier Excel se trouve dans le même répertoire que le script ou fournissez le chemin complet.")
    exit()
except Exception as e:
    print(f"Une erreur inattendue s'est produite lors de la lecture du fichier Excel : {e}")
    print("Vous pourriez avoir besoin d'installer la bibliothèque 'openpyxl' : pip install openpyxl")
    exit()

# --- Nettoyage et Préparation des Données pour Cashflow ---
if not cashflow_df.empty:
    print("\n--- Nettoyage des données de Cashflow ---")
    # Supposer que la première colonne contient les noms des métriques si elle n'est pas nommée ou si elle est "Unnamed: 0"
    # Et que les années sont en colonnes, nécessitant une transposition.
    potential_metric_col_name = cashflow_df.columns[0]
    if 'Unnamed: 0' in potential_metric_col_name or cashflow_df[potential_metric_col_name].dtype == 'object':
        # Vérifier si les autres colonnes ressemblent à des années (numériques ou chaînes convertibles)
        looks_like_years_as_cols = all(pd.api.types.is_numeric_dtype(cashflow_df[col]) or str(col).isdigit() for col in cashflow_df.columns[1:5]) # Check first few cols
        
        if looks_like_years_as_cols or cashflow_df.shape[0] < cashflow_df.shape[1]: # More cols than rows often means metrics are rows
            print(f"Transposition de la feuille Cashflow, '{potential_metric_col_name}' semble être la colonne des métriques.")
            cashflow_df = cashflow_df.rename(columns={potential_metric_col_name: 'Metric'})
            cashflow_df = cashflow_df.set_index('Metric')
            cashflow_df = cashflow_df.transpose()
            cashflow_df.index.name = 'Year'
            cashflow_df = cashflow_df.reset_index()
        else:
            print("La feuille Cashflow ne semble pas nécessiter de transposition immédiate basée sur la structure des colonnes.")
            # On s'attend à ce que 'Year' soit une colonne ou que les index soient des années.
            if 'Year' not in cashflow_df.columns and 'Année' not in cashflow_df.columns:
                 if pd.api.types.is_numeric_dtype(cashflow_df.index):
                     cashflow_df = cashflow_df.reset_index().rename(columns={'index':'Year'})


    # Standardiser le nom de la colonne Année
    if 'Année' in cashflow_df.columns and 'Year' not in cashflow_df.columns:
        cashflow_df = cashflow_df.rename(columns={'Année': 'Year'})
    
    # Conversion numérique des colonnes pertinentes
    for col in cashflow_df.columns:
        if col.lower() not in ['year', 'metric', 'description', 'item', 'année']:
            cashflow_df[col] = pd.to_numeric(cashflow_df[col], errors='coerce')

    if 'Year' in cashflow_df.columns:
        cashflow_df['Year'] = pd.to_numeric(cashflow_df['Year'], errors='coerce')
        cashflow_df = cashflow_df.dropna(subset=['Year']) # Enlever les lignes où Année est NaN après conversion
        cashflow_df = cashflow_df.sort_values(by='Year')
        print("Données de Cashflow nettoyées et triées par année.")
        # print(cashflow_df.head())
        # print(cashflow_df.info())

    else:
        print("AVERTISSEMENT : Colonne 'Year' (ou 'Année') non trouvée ou non traitée correctement dans Cashflow. Les graphiques temporels pourraient échouer.")
        print("Colonnes disponibles dans Cashflow:", cashflow_df.columns.tolist())


# --- Nettoyage et Préparation des Données pour KPIs ---
kpi_name_col_fr = None
kpi_value_col_fr = None
if not kpis_df.empty:
    print("\n--- Nettoyage des données KPIs ---")
    kpis_df.columns = kpis_df.columns.str.strip()
    # Tentative d'identification flexible des colonnes KPI et Valeur
    # Priorité aux noms français courants
    if 'Indicateur' in kpis_df.columns and 'Valeur' in kpis_df.columns:
        kpi_name_col_fr = 'Indicateur'
        kpi_value_col_fr = 'Valeur'
    elif 'KPI' in kpis_df.columns and 'Value' in kpis_df.columns:
        kpi_name_col_fr = 'KPI'
        kpi_value_col_fr = 'Value'
    elif 'Metric' in kpis_df.columns and 'Value' in kpis_df.columns: # Anglais
        kpi_name_col_fr = 'Metric'
        kpi_value_col_fr = 'Value'
    elif len(kpis_df.columns) >= 2:
        # Si la première colonne est textuelle et la deuxième semble numérique
        col1_name = kpis_df.columns[0]
        col2_name = kpis_df.columns[1]
        # Essayer de convertir la deuxième colonne en numérique (après nettoyage de % etc.)
        temp_val_col = kpis_df[col2_name].astype(str).str.replace('%', '', regex=False).str.replace(',', '.', regex=False).str.replace(r'\s+', '', regex=True)
        if pd.to_numeric(temp_val_col, errors='coerce').notna().sum() > kpis_df.shape[0] / 2 : # Si plus de la moitié sont numériques
            print(f"Utilisation des colonnes '{col1_name}' comme Nom du KPI et '{col2_name}' comme Valeur du KPI par déduction.")
            kpis_df = kpis_df.rename(columns={col1_name: 'Indicateur_AutoGen', col2_name: 'Valeur_AutoGen'})
            kpi_name_col_fr = 'Indicateur_AutoGen'
            kpi_value_col_fr = 'Valeur_AutoGen'

    if kpi_name_col_fr and kpi_value_col_fr:
        print(f"Colonnes KPI identifiées : Nom='{kpi_name_col_fr}', Valeur='{kpi_value_col_fr}'")
        kpis_df[kpi_name_col_fr] = kpis_df[kpi_name_col_fr].astype(str).str.strip()
        if kpis_df[kpi_value_col_fr].dtype == 'object':
            # Gérer les nombres français (virgule comme décimal) et pourcentages
            kpis_df[kpi_value_col_fr] = kpis_df[kpi_value_col_fr].astype(str).str.replace('%', '', regex=False).str.replace(',', '.', regex=False).str.replace(r'\s+', '', regex=True)
        kpis_df[kpi_value_col_fr] = pd.to_numeric(kpis_df[kpi_value_col_fr], errors='coerce')
        kpis_df = kpis_df.dropna(subset=[kpi_name_col_fr, kpi_value_col_fr])
        print("Données KPIs nettoyées.")
        # print(kpis_df.head())
    else:
        print("AVERTISSEMENT : Impossible d'identifier les colonnes Nom et Valeur du KPI. Le graphique des KPIs pourrait échouer.")
        print("Colonnes disponibles dans KPIs:", kpis_df.columns.tolist())


# --- Nettoyage et Préparation des Données pour Sensitivity ---
if not sensitivity_df.empty:
    print("\n--- Nettoyage des données de Sensibilité ---")
    # Si la première colonne est 'Unnamed: 0' ou similaire, la définir comme index
    if sensitivity_df.columns[0].lower().startswith('unnamed') or sensitivity_df.columns[0] in ['Metric', 'Indicateur', 'Facteur de Sensibilité']:
        sensitivity_df = sensitivity_df.rename(columns={sensitivity_df.columns[0]: 'Facteur_Sensibilite'})
        sensitivity_df = sensitivity_df.set_index('Facteur_Sensibilite')
    elif not pd.api.types.is_numeric_dtype(sensitivity_df.iloc[:, 0]):
         sensitivity_df = sensitivity_df.set_index(sensitivity_df.columns[0])

    for col in sensitivity_df.columns:
        if sensitivity_df[col].dtype == 'object':
            is_percentage = sensitivity_df[col].astype(str).str.contains('%', na=False)
            cleaned_col = sensitivity_df[col].astype(str).str.rstrip('%').str.replace(',', '.', regex=False).str.replace(r'\s+', '', regex=True)
            numeric_col = pd.to_numeric(cleaned_col, errors='coerce')
            sensitivity_df[col] = np.where(is_percentage & numeric_col.notna(), numeric_col / 100.0, numeric_col)
        else:
            sensitivity_df[col] = pd.to_numeric(sensitivity_df[col], errors='coerce')
    print("Données de Sensibilité nettoyées.")
    # print(sensitivity_df.head())


plt.style.use('seaborn-v0_8-whitegrid')
FIGURE_COUNT = 0

# --- GRAPHIQUES ---

# Graphique 1: Flux de Trésorerie Disponibles Cumulés pour l'Actionnaire (FCFE)
print("\n--- Génération Graphique 1: FCFE Cumulé ---")
if not cashflow_df.empty and 'Year' in cashflow_df.columns:
    fcfe_col_candidates = ['Free Cash Flow to Equity', 'FCFE', 'Flux de trésorerie disponible pour les actionnaires', 'Flux de Trésorerie aux Actionnaires']
    fcfe_col_name = next((col for col in cashflow_df.columns if any(c.lower() in col.lower() for c in fcfe_col_candidates)), None)

    if fcfe_col_name and pd.api.types.is_numeric_dtype(cashflow_df[fcfe_col_name]):
        cumulative_fcfe = cashflow_df[fcfe_col_name].cumsum()
        plt.figure(figsize=(12, 7))
        plt.plot(cashflow_df['Year'], cumulative_fcfe, marker='o', linestyle='-', color='green')
        plt.title('Flux de Trésorerie Disponibles Cumulés pour l\'Actionnaire (FCFE) 💰', fontsize=15)
        plt.xlabel('Année', fontsize=12)
        plt.ylabel('FCFE Cumulé (en unités monétaires)', fontsize=12)
        plt.axhline(0, color='black', linewidth=0.5, linestyle='--')
        plt.grid(True, which='both', linestyle='--', linewidth=0.5)
        plt.tight_layout()
        plt.savefig("graphique_fcfe_cumule.png")
        plt.show()
        FIGURE_COUNT += 1
        print("Graphique 'graphique_fcfe_cumule.png' généré.")
    else:
        print(f"Impossible de générer le graphique FCFE Cumulé. Colonne FCFE ('{fcfe_col_name}') non trouvée ou non numérique.")
        print(f"Colonnes de type numérique dans cashflow_df: {[col for col in cashflow_df.columns if pd.api.types.is_numeric_dtype(cashflow_df[col])]}")
else:
    print("Impossible de générer le graphique FCFE: Données de Cashflow ou colonne 'Year' manquantes.")


# Graphique 2: Composants Clés du Flux de Trésorerie
print("\n--- Génération Graphique 2: Composants Clés du Flux de Trésorerie ---")
if not cashflow_df.empty and 'Year' in cashflow_df.columns:
    rev_candidates = ['Total Revenues', 'Revenus Totaux', 'Chiffre d\'affaires']
    opex_candidates = ['Total Opex', 'Total Operating Expenses', 'Coûts d\'exploitation totaux', 'Total Charges d\'Exploitation']
    debt_candidates = ['Total Debt Service', 'Service de la Dette Total', 'Service de la dette']
    
    rev_col = next((col for col in cashflow_df.columns if any(c.lower() in col.lower() for c in rev_candidates)), None)
    opex_col = next((col for col in cashflow_df.columns if any(c.lower() in col.lower() for c in opex_candidates)), None)
    debt_col = next((col for col in cashflow_df.columns if any(c.lower() in col.lower() for c in debt_candidates)), None)

    plotted_something = False
    plt.figure(figsize=(12, 7))
    if rev_col and pd.api.types.is_numeric_dtype(cashflow_df[rev_col]):
        plt.plot(cashflow_df['Year'], cashflow_df[rev_col], label=f'{rev_col} ☀️', marker='^', linestyle='-')
        plotted_something = True
    else: print(f"Colonne Revenus ('{rev_col}') non trouvée ou non numérique.")
    
    if opex_col and pd.api.types.is_numeric_dtype(cashflow_df[opex_col]):
        plt.plot(cashflow_df['Year'], cashflow_df[opex_col], label=f'{opex_col} 🛠️', marker='s', linestyle='--')
        plotted_something = True
    else: print(f"Colonne Opex ('{opex_col}') non trouvée ou non numérique.")

    if debt_col and pd.api.types.is_numeric_dtype(cashflow_df[debt_col]):
        plt.plot(cashflow_df['Year'], cashflow_df[debt_col], label=f'{debt_col} 🏦', marker='x', linestyle=':')
        plotted_something = True
    else: print(f"Colonne Service de la Dette ('{debt_col}') non trouvée ou non numérique.")

    if plotted_something:
        plt.title('Composants Clés du Flux de Trésorerie Annuels', fontsize=15)
        plt.xlabel('Année', fontsize=12)
        plt.ylabel('Montant (en unités monétaires)', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, which='both', linestyle='--', linewidth=0.5)
        plt.tight_layout()
        plt.savefig("graphique_composants_cashflow.png")
        plt.show()
        FIGURE_COUNT += 1
        print("Graphique 'graphique_composants_cashflow.png' généré.")
    else:
        print("Impossible de générer le graphique des Composants Clés du Flux de Trésorerie. Aucune donnée pertinente trouvée.")
else:
    print("Impossible de générer le graphique des Composants Clés: Données de Cashflow ou colonne 'Year' manquantes.")


# Graphique 3: Indicateurs de Performance Clés (KPIs)
print("\n--- Génération Graphique 3: Indicateurs de Performance Clés (KPIs) ---")
if not kpis_df.empty and kpi_name_col_fr and kpi_value_col_fr:
    # Filtrer les KPIs pour ceux qui sont numériques et pertinents
    kpi_keywords_fr = ['IRR', 'TRI', 'VAN', 'NPV', 'DSCR', 'Payback', 'Délai de récupération', 'Marge']
    
    kpis_to_plot_df = kpis_df[
        kpis_df[kpi_name_col_fr].str.contains('|'.join(kpi_keywords_fr), case=False, na=False) &
        pd.to_numeric(kpis_df[kpi_value_col_fr], errors='coerce').notna()
    ].copy()
    
    kpis_to_plot_df[kpi_value_col_fr] = pd.to_numeric(kpis_to_plot_df[kpi_value_col_fr], errors='coerce')

    if not kpis_to_plot_df.empty:
        percentage_keywords_fr = ['IRR', 'TRI', 'Marge', 'ROE', 'Taux']
        kpis_to_plot_df['Est_Pourcentage'] = kpis_to_plot_df[kpi_name_col_fr].apply(
            lambda x: any(pkpi.lower() in x.lower() for pkpi in percentage_keywords_fr)
        )
        
        # Standardisation des pourcentages (si > 1, diviser par 100)
        kpis_to_plot_df[kpi_value_col_fr] = kpis_to_plot_df.apply(
            lambda row: row[kpi_value_col_fr] / 100.0 if row['Est_Pourcentage'] and abs(row[kpi_value_col_fr]) > 1.5 else row[kpi_value_col_fr], # 1.5 to be safe with small percentages already in decimal
            axis=1
        )
        
        # Trier pour une meilleure visualisation
        kpis_to_plot_df = kpis_to_plot_df.sort_values(by=kpi_value_col_fr, ascending=False)


        fig, ax1 = plt.subplots(figsize=(14, 8))
        bars = ax1.bar(kpis_to_plot_df[kpi_name_col_fr], kpis_to_plot_df[kpi_value_col_fr], color=['skyblue', 'lightcoral', 'lightgreen', 'gold', 'plum', 'lightsalmon', 'cyan', 'magenta'])

        ax1.set_ylabel('Valeur', fontsize=12)
        ax1.set_title('Indicateurs de Performance Clés (KPIs) 📊', fontsize=15)
        plt.xticks(rotation=45, ha="right", fontsize=10)

        for i, bar in enumerate(bars):
            yval = bar.get_height()
            is_percentage = kpis_to_plot_df['Est_Pourcentage'].iloc[i]
            label_text = f'{yval:.2%}' if is_percentage else f'{yval:,.2f}'
            ax1.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01 * np.sign(yval if yval != 0 else 1) * (ax1.get_ylim()[1] - ax1.get_ylim()[0]), label_text, ha='center', va='bottom' if yval >=0 else 'top', fontsize=9)
        
        if kpis_to_plot_df['Est_Pourcentage'].all() and not kpis_to_plot_df.empty:
             ax1.yaxis.set_major_formatter(mtick.PercentFormatter(xmax=1.0))

        plt.grid(True, axis='y', linestyle='--', linewidth=0.5)
        plt.tight_layout()
        plt.savefig("graphique_kpi_resume.png")
        plt.show()
        FIGURE_COUNT += 1
        print("Graphique 'graphique_kpi_resume.png' généré.")
    else:
        print("Impossible de générer le graphique des KPIs. Aucun KPI pertinent trouvé ou données manquantes après filtrage.")
        if kpi_name_col_fr in kpis_df:
             print("KPIs originaux (colonne nom):", kpis_df[kpi_name_col_fr].tolist())
else:
    print("Impossible de générer le graphique des KPIs: Colonnes Nom/Valeur non identifiées ou données KPI vides.")

# Graphique 4 & 5: Analyse de Sensibilité (TRI Projet et TRI Actionnaire)
print("\n--- Génération Graphiques 4 & 5: Analyse de Sensibilité ---")
if not sensitivity_df.empty:
    for kpi_key in ['Equity IRR', 'Project IRR']: # Peut être adapté si les noms sont différents
        # Essayer de trouver les lignes correspondantes dans l'index (avec flexibilité pour les noms)
        # Ex: "Equity IRR @Base", "Equity IRR @+10%", "Equity IRR @-10%"
        # Ou en français: "TRI Actionnaire @Base", etc.
        
        base_label_candidates = [f"{kpi_key} @Base", f"{kpi_key} Base", f"{kpi_key} - Base"]
        plus_10_label_candidates = [f"{kpi_key} @+10%", f"{kpi_key} +10%"]
        minus_10_label_candidates = [f"{kpi_key} @-10%", f"{kpi_key} -10%"]

        base_row_label = next((label for label in sensitivity_df.index if any(c.lower() == label.lower() for c in base_label_candidates)), None)
        plus_10_label = next((label for label in sensitivity_df.index if any(c.lower() == label.lower() for c in plus_10_label_candidates)), None)
        minus_10_label = next((label for label in sensitivity_df.index if any(c.lower() == label.lower() for c in minus_10_label_candidates)), None)
        
        # Remplacer par des noms français pour le titre du graphique
        kpi_display_name_fr = "TRI Actionnaire" if "equity" in kpi_key.lower() else "TRI Projet" if "project" in kpi_key.lower() else kpi_key

        if base_row_label and plus_10_label and minus_10_label:
            print(f"Préparation de l'analyse de sensibilité pour : {kpi_display_name_fr}")
            print(f"  Ligne Base: '{base_row_label}', Ligne +10%: '{plus_10_label}', Ligne -10%: '{minus_10_label}'")
            
            base_values = sensitivity_df.loc[base_row_label]
            plus_10_values = sensitivity_df.loc[plus_10_label]
            minus_10_values = sensitivity_df.loc[minus_10_label]

            variables_sens = sensitivity_df.columns.tolist()
            
            plot_data_sens = []
            for var_sens in variables_sens:
                if pd.api.types.is_numeric_dtype(base_values[var_sens]) and \
                   pd.api.types.is_numeric_dtype(plus_10_values[var_sens]) and \
                   pd.api.types.is_numeric_dtype(minus_10_values[var_sens]):
                    plot_data_sens.append({'Variable': var_sens, 'Variation': '+10%', f'{kpi_key}': plus_10_values[var_sens], 'Base': base_values[var_sens]})
                    plot_data_sens.append({'Variable': var_sens, 'Variation': '-10%', f'{kpi_key}': minus_10_values[var_sens], 'Base': base_values[var_sens]})
                else:
                    print(f"  Skipping variable '{var_sens}' pour {kpi_key} due à des données non-numériques.")

            if not plot_data_sens:
                print(f"  Aucune donnée numérique valide pour tracer la sensibilité de {kpi_key}.")
                continue

            sensitivity_plot_df = pd.DataFrame(plot_data_sens)

            plt.figure(figsize=(14, 8))
            x_sens = np.arange(len(variables_sens))
            width_sens = 0.35

            # Filtrer pour les variations
            plus_10_data = sensitivity_plot_df[sensitivity_plot_df['Variation'] == '+10%'][kpi_key]
            minus_10_data = sensitivity_plot_df[sensitivity_plot_df['Variation'] == '-10%'][kpi_key]
            
            # Assurer que les données correspondent aux variables (au cas où certaines ont été skippées)
            valid_vars_sens = sensitivity_plot_df['Variable'].unique()
            x_sens_valid = np.arange(len(valid_vars_sens))

            if not plus_10_data.empty:
                 plt.bar(x_sens_valid - width_sens/2, plus_10_data, width_sens, label='Variation +10%', color='lightgreen')
            if not minus_10_data.empty:
                 plt.bar(x_sens_valid + width_sens/2, minus_10_data, width_sens, label='Variation -10%', color='lightcoral')


            for i, var_s_valid in enumerate(valid_vars_sens):
                # Récupérer la valeur de base pour cette variable valide
                base_val_s = sensitivity_df.loc[base_row_label, var_s_valid]
                if pd.api.types.is_numeric_dtype(base_val_s):
                    plt.hlines(base_val_s, x_sens_valid[i] - width_sens, x_sens_valid[i] + width_sens, colors='dodgerblue', linestyles='dashed', label='Base Case IRR' if i == 0 else "")

            plt.ylabel(f'Valeur {kpi_display_name_fr}', fontsize=12)
            plt.xlabel('Variable de Sensibilité', fontsize=12)
            plt.title(f'Analyse de Sensibilité pour {kpi_display_name_fr} ⚖️', fontsize=15)
            plt.xticks(x_sens_valid, labels=valid_vars_sens, rotation=45, ha="right", fontsize=10)
            plt.gca().yaxis.set_major_formatter(mtick.PercentFormatter(xmax=1.0)) # Assume IRR is a percentage
            if not plus_10_data.empty or not minus_10_data.empty : plt.legend()
            plt.grid(True, axis='y', linestyle='--', linewidth=0.5)
            plt.tight_layout()
            filename = f"graphique_sensibilite_{kpi_key.lower().replace(' ', '_')}.png"
            plt.savefig(filename)
            plt.show()
            FIGURE_COUNT += 1
            print(f"Graphique '{filename}' généré.")
        else:
            print(f"Impossible de générer l'Analyse de Sensibilité pour '{kpi_display_name_fr}'.")
            print(f"  Étiquettes de lignes requises non trouvées dans l'index de la feuille Sensibilité.")
            print(f"  Recherché (exemples): '{kpi_key} @Base', '{kpi_key} @+10%', '{kpi_key} @-10%'")
            print(f"  Index disponible : {sensitivity_df.index.tolist()}")
else:
    print("Impossible de générer les graphiques d'Analyse de Sensibilité: Données de Sensibilité vides.")


# Graphique 6: Quelques éléments de la feuille "Inputs" (si pertinent)
print("\n--- Génération Graphique 6: Visualisation des Données d'Entrée (Inputs) ---")
if not inputs_df.empty:
    # Exemple : Si vous avez des coûts d'investissement décomposés.
    # Chercher des colonnes qui pourraient représenter une décomposition des coûts.
    # Ceci est très dépendant de la structure de votre feuille "Inputs".
    # Adapter les `input_cost_item_col` et `input_cost_value_col` ci-dessous.

    # Tentative de trouver des colonnes de description et de valeur
    desc_col_input = None
    val_col_input = None

    # Chercher des paires de colonnes (texte, numérique)
    for i in range(len(inputs_df.columns) -1):
        col1 = inputs_df.columns[i]
        col2 = inputs_df.columns[i+1]
        if inputs_df[col1].dtype == 'object' and pd.api.types.is_numeric_dtype(inputs_df[col2]):
            # S'assurer qu'il y a plusieurs valeurs numériques dans la colonne
            if inputs_df[col2].count() > 2 : # Au moins 3 valeurs numériques pour un graphique pertinent
                desc_col_input = col1
                val_col_input = col2
                break
    
    if desc_col_input and val_col_input:
        print(f"Tentative de graphique à partir des Inputs : '{desc_col_input}' et '{val_col_input}'")
        # Filtrer les valeurs non nulles et positives pour un graphique de coûts par exemple
        plot_inputs_df = inputs_df[[desc_col_input, val_col_input]].copy()
        plot_inputs_df[val_col_input] = pd.to_numeric(plot_inputs_df[val_col_input], errors='coerce')
        plot_inputs_df = plot_inputs_df.dropna()
        plot_inputs_df = plot_inputs_df[plot_inputs_df[val_col_input] > 0]
        
        # Trier pour une meilleure visualisation
        plot_inputs_df = plot_inputs_df.sort_values(by=val_col_input, ascending=False)


        if not plot_inputs_df.empty and len(plot_inputs_df) > 1 : # Au moins 2 items pour un bar chart
            plt.figure(figsize=(12, 7))
            plt.bar(plot_inputs_df[desc_col_input], plot_inputs_df[val_col_input], color='teal')
            plt.title('Exemple de Visualisation des Données d\'Entrée (Inputs) ⚙️', fontsize=15)
            plt.xlabel(desc_col_input, fontsize=12)
            plt.ylabel(val_col_input, fontsize=12)
            plt.xticks(rotation=45, ha="right", fontsize=10)
            plt.grid(True, axis='y', linestyle='--', linewidth=0.5)
            plt.tight_layout()
            plt.savefig("graphique_inputs_exemple.png")
            plt.show()
            FIGURE_COUNT += 1
            print("Graphique 'graphique_inputs_exemple.png' généré.")
        else:
            print(f"Pas assez de données valides (positives, numériques) dans '{desc_col_input}'/'{val_col_input}' pour un graphique d'Inputs pertinent.")
    else:
        print("Impossible de trouver automatiquement des colonnes appropriées (description textuelle, valeur numérique) dans la feuille Inputs pour un graphique.")
else:
    print("Feuille Inputs vide ou non chargée, graphique des Inputs ignoré.")


print(f"\n--- Exécution du script terminée. {FIGURE_COUNT} graphique(s) généré(s) et sauvegardé(s) en PNG. ---")
print("Vérifiez les messages ci-dessus pour toute erreur ou avertissement concernant des graphiques spécifiques.")