% Mémoire de fin d'études - Master en Comptabilité, Contrôle de Gestion et Audit
% Fondements Stratégiques et Juridiques des Investissements Italiens au Maroc dans le Cadre du Piano Mattei

\documentclass[12pt,a4paper]{report}

% Packages nécessaires
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
% Conditional compilation for pdfLaTeX vs XeLaTeX
\usepackage{iftex}

\ifPDFTeX
    % pdfLaTeX setup
    \usepackage[utf8]{inputenc}
    \usepackage[T1]{fontenc}
    \usepackage[main=french,english]{babel}
    % Arabic text will show as transliteration
\else
    % XeLaTeX setup
    \usepackage{fontspec}
    \usepackage{polyglossia}
    \setmainlanguage{french}
    \setotherlanguage{arabic}
    % Set up Arabic font with fallback options
    \newfontfamily\arabicfont[Script=Arabic,Scale=1.0]{Amiri}
    % Use the existing \textarabic command from polyglossia
\fi
\usepackage{graphicx}
\usepackage[pdftex,
    pdfauthor={<PERSON><PERSON><PERSON><PERSON>},
    pdftitle={Les considérations fiscales et financières pour les entreprises italiennes investissant dans les projets d'énergie renouvelable au Maroc sous le Piano Mattei},
    pdfsubject={Mémoire de Master},
    pdfkeywords={Piano Mattei, Maroc, Italie, Énergies renouvelables, Investissements},
    pdfproducer={LaTeX},
    pdfcreator={pdfLaTeX},
    colorlinks=true,
    linkcolor=black,
    citecolor=black,
    urlcolor=black,
    filecolor=black
]{hyperref}
\usepackage{setspace}
\usepackage{titlesec}
\usepackage{tocloft}
\usepackage{tocbibind}  % Helps with handling TOC entries
\usepackage{longtable}  % For long tables that span multiple pages
\usepackage{fancyhdr}
\usepackage{geometry}
\usepackage{indentfirst}
\usepackage[round, sort&compress, authoryear]{natbib}
\setcitestyle{aysep={,}}
\renewcommand{\bibsection}{\chapter*{Bibliographie}\markboth{BIBLIOGRAPHIE}{BIBLIOGRAPHIE}}
\usepackage{xcolor}
\usepackage{appendix}
%\usepackage{minitoc} % Pour le sommaire au début et la table des matières à la fin
\usepackage{etoc} % For multiple and advanced Tables of Contents
\usepackage{newtxtext} % Times New Roman pour le texte
\usepackage{newtxmath} % Times New Roman pour les formules mathématiques
\usepackage{textcomp} % For symbols
\usepackage{newunicodechar}

% Arabic support - remove character mappings to display actual Arabic % For unicode fixes
\usepackage{microtype} % Better typesetting
\usepackage{tabularx} % For better table control
\usepackage{booktabs} % For professional-looking tables
\usepackage{makecell} % For better table cell formatting
\usepackage{multirow} % For cells spanning multiple rows
\usepackage{float} % For H table positioning
\usepackage{pgfplots} % For creating advanced plots with axis environment
\pgfplotsset{compat=1.18} % Set compatibility mode

% Unicode fixes
\newunicodechar{₂}{$_2$}
\newunicodechar{₃}{$_3$}
\newunicodechar{≥}{\ensuremath{\geq}}
\newunicodechar{–}{--}
\newunicodechar{−}{-}
\newunicodechar{•}{\textbullet\ }

% Ajout des packages pour les diagrammes et figures
\usepackage{tikz}
\usetikzlibrary{shapes,arrows,positioning,fit,backgrounds,patterns,decorations.pathreplacing,calc,shadows}
\usetikzlibrary{arrows.meta}
\usepackage{array}
\usepackage{booktabs}

% Define enhanced colors for Piano Mattei diagram
\definecolor{maincolor}{RGB}{0,102,153}
\definecolor{pillarcolor}{RGB}{30,144,255}
\definecolor{boxcolor}{RGB}{240,240,240}
\definecolor{boxborder}{RGB}{200,200,200}
\definecolor{italyflag}{RGB}{0,146,70}
\definecolor{italyflagred}{RGB}{206,43,55}
\definecolor{africacolor}{RGB}{211,134,0}
\definecolor{brown}{RGB}{0,0,0}  % Define brown as black for cover page

% Define boxedtext environment for case studies
\usepackage{mdframed}
\newenvironment{boxedtext}{
  \begin{mdframed}[
    linewidth=1pt,
    linecolor=gray!50,
    backgroundcolor=gray!10,
    innertopmargin=10pt,
    innerbottommargin=10pt,
    innerrightmargin=10pt,
    innerleftmargin=10pt,
    roundcorner=5pt
  ]}
  {\end{mdframed}}

% Configuration de la mise en page
\geometry{margin=2.5cm}
\setlength{\parindent}{1.5em}
\setstretch{1.5}

% Configuration des titres
\titleformat{\chapter}[display]
{\normalfont\huge\bfseries\centering}{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titlespacing*{\chapter}{0pt}{50pt}{40pt}

% Configuration des en-têtes et pieds de page
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\slshape\nouppercase{\leftmark}}
\fancyhead[R]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}
\setlength{\headheight}{15pt} % Fix header height

% Configuration du sommaire
\renewcommand{\contentsname}{Sommaire}

% Define custom TOCs using tocloft
\newcommand{\shorttableofcontents}{%
  \begingroup
  \renewcommand{\contentsname}{Sommaire}
  % Settings for short TOC
  \setcounter{tocdepth}{1} % Show only chapters and sections
  \tableofcontents
  \endgroup
}

% Define command for the detailed TOC at the end
\newcommand{\detailedtableofcontents}{%
  \begingroup
  \renewcommand{\contentsname}{Table des Matières}
  % Use bold chapter titles in TOC with color
  \renewcommand{\cftchapfont}{\bfseries\color{bibcolor}}
  \renewcommand{\cftchappagefont}{\bfseries}
  % Change spacing
  \setlength{\cftbeforechapskip}{1em}
  \setlength{\cftbeforesecskip}{0.5em}
  % Show more detail in the TOC
  \setcounter{tocdepth}{3} % Show chapters, sections, and subsections
  \tableofcontents
  \endgroup
}

% Activation des mini-tables des matières
%\dominitoc
%\nomtcrule % Pas de ligne sous les mini-TOC

% Custom bibliography styling
\definecolor{bibcolor}{RGB}{0, 51, 102}  % Dark blue color for the bibliography
\renewcommand{\bibname}{\textcolor{bibcolor}{Références bibliographiques}}

% Custom citation style in footnotes
\makeatletter
\newcommand{\footcite}[1]{\footnote{\cite{#1}}}
\newcommand{\footcitealp}[1]{\footnote{\citealp{#1}}}
\makeatother

% Début du document
\begin{document}

% Pages préliminaires
\input{preliminaries/page_de_garde}
\input{preliminaries/dedicace}
\input{preliminaries/remerciements}
\input{preliminaries/abstract}
%\input{preliminaries/abstract_arabic}
\input{preliminaries/resume}
\input{preliminaries/abbreviations}

% Sommaire (version abrégée de la table des matières)
\chapter*{Sommaire}
\addcontentsline{toc}{chapter}{Sommaire}
\markboth{SOMMAIRE}{SOMMAIRE}
% Short table of contents
\etocsettocstyle{\vskip 0.5em}{\vskip 1em}
\etocsetnexttocdepth{section}
\shorttableofcontents
\clearpage

% Liste des figures et tableaux
\listoffigures
\clearpage
\listoftables
\clearpage

% Introduction générale
\input{introduction/introduction_generale}

% Partie 1
\part{Cadre stratégique et juridique des IDE italiens au Maroc}
%\minitoc
\input{partie1/chapitre1}
\input{partie1/chapitre2}
\input{partie1/chapitre3}

% Partie 2
\part{Étude de cas Agevolami.it : accompagnement et recommandations}
\input{partie2/chapitre1}
\input{partie2/chapitre2}
\input{partie2/chapitre3}

% Conclusion générale
\input{conclusion/conclusion_generale}

% Bibliographie
\renewcommand{\bibfont}{\small}
\setlength{\bibsep}{0.7ex}
\setlength{\bibhang}{2em}
\bibliographystyle{apalike}
\bibliography{bibliography/references}

% Annexes
\begin{appendices}
\input{annexes/annexe_technique}
%\input{annexes/annexe1}
\end{appendices}

% Table des matières à la fin
\cleardoublepage
\chapter*{Table des Matières}
\addcontentsline{toc}{chapter}{Table des Matières}
\markboth{TABLE DES MATIÈRES}{TABLE DES MATIÈRES}
\detailedtableofcontents

\end{document}
