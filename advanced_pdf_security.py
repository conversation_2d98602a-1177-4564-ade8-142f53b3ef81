#!/usr/bin/env python3
"""
Advanced PDF Security Tool with Self-Destruct Feature
Creates a secure PDF viewer application with:
- Password protection
- Failed attempt tracking
- Self-destruct after 3 failed attempts
- Secure logging and monitoring
- Enhanced protection mechanisms
"""

import sys
import os
import json
import hashlib
import tempfile
import shutil
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, simpledialog
import PyPDF2
import fitz  # PyMuPDF for better PDF rendering
import threading
import time

class SecurePDFViewer:
    def __init__(self, pdf_path, security_config=None):
        self.pdf_path = pdf_path
        self.temp_dir = None
        self.is_authenticated = False
        self.attempt_count = 0
        self.max_attempts = 3
        self.lockout_duration = 24  # hours
        
        # Security configuration
        self.config = security_config or {
            "author": "Document Author",
            "title": "Secure Document",
            "max_attempts": 3,
            "lockout_hours": 24,
            "auto_destruct": True,
            "log_attempts": True,
            "require_network": False
        }
        
        # Create secure storage for attempt tracking
        self.security_file = self._get_security_file_path()
        self.load_security_data()
        
        # Initialize GUI
        self.root = None
        self.pdf_document = None
        
    def _get_security_file_path(self):
        """Get the path for storing security data"""
        # Create a hidden security file based on PDF hash
        pdf_hash = self._get_file_hash(self.pdf_path)
        security_dir = Path.home() / ".pdf_security"
        security_dir.mkdir(exist_ok=True)
        return security_dir / f"{pdf_hash}.sec"
    
    def _get_file_hash(self, file_path):
        """Generate hash of the PDF file for identification"""
        with open(file_path, 'rb') as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()[:16]
        return file_hash
    
    def load_security_data(self):
        """Load security tracking data"""
        try:
            if self.security_file.exists():
                with open(self.security_file, 'r') as f:
                    data = json.load(f)
                    self.attempt_count = data.get('attempts', 0)
                    last_attempt = data.get('last_attempt')
                    
                    # Check if we're still in lockout period
                    if last_attempt and self.attempt_count >= self.max_attempts:
                        last_time = datetime.fromisoformat(last_attempt)
                        if datetime.now() - last_time < timedelta(hours=self.lockout_duration):
                            self._trigger_lockout()
                            return
                        else:
                            # Reset attempts after lockout period
                            self.attempt_count = 0
        except Exception as e:
            print(f"Error loading security data: {e}")
    
    def save_security_data(self):
        """Save security tracking data"""
        try:
            data = {
                'attempts': self.attempt_count,
                'last_attempt': datetime.now().isoformat(),
                'pdf_path': str(self.pdf_path),
                'config': self.config
            }
            with open(self.security_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            print(f"Error saving security data: {e}")
    
    def _trigger_lockout(self):
        """Trigger security lockout"""
        messagebox.showerror(
            "Security Lockout",
            f"This document has been locked due to {self.max_attempts} failed attempts.\n"
            f"Access will be restored in {self.lockout_duration} hours.\n\n"
            "If you are the legitimate owner, please contact the document author."
        )
        sys.exit(1)
    
    def _trigger_self_destruct(self):
        """Trigger self-destruct sequence"""
        if not self.config.get('auto_destruct', True):
            return
            
        try:
            # Show warning message
            messagebox.showerror(
                "Security Breach Detected",
                "SECURITY BREACH DETECTED!\n\n"
                f"{self.max_attempts} failed password attempts detected.\n"
                "Initiating security protocol...\n\n"
                "Document access permanently disabled.\n"
                "Contact the document author if this was an error."
            )
            
            # Log the security event
            self._log_security_event("SELF_DESTRUCT_TRIGGERED")
            
            # Secure deletion of tracking files
            self._secure_delete_files()
            
            # Create evidence file
            self._create_breach_report()
            
            # Exit application
            messagebox.showinfo(
                "Security Protocol Complete",
                "Security protocol completed.\n"
                "Application will now terminate."
            )
            
        except Exception as e:
            print(f"Error during self-destruct: {e}")
        finally:
            sys.exit(1)
    
    def _secure_delete_files(self):
        """Securely delete security tracking files"""
        try:
            if self.security_file.exists():
                # Overwrite file with random data before deletion
                file_size = self.security_file.stat().st_size
                with open(self.security_file, 'wb') as f:
                    f.write(os.urandom(file_size))
                self.security_file.unlink()
            
            # Clean up any temporary files
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                
        except Exception as e:
            print(f"Error during secure deletion: {e}")
    
    def _create_breach_report(self):
        """Create a security breach report"""
        try:
            report_file = Path.home() / "Desktop" / f"SECURITY_BREACH_REPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w') as f:
                f.write("=" * 60 + "\n")
                f.write("🚨 SECURITY BREACH REPORT 🚨\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Document: {os.path.basename(self.pdf_path)}\n")
                f.write(f"Author: {self.config.get('author', 'Unknown')}\n")
                f.write(f"Breach Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Failed Attempts: {self.attempt_count}\n")
                f.write(f"Computer: {os.environ.get('COMPUTERNAME', 'Unknown')}\n")
                f.write(f"User: {os.environ.get('USERNAME', 'Unknown')}\n\n")
                f.write("SECURITY ACTIONS TAKEN:\n")
                f.write("• Document access permanently disabled\n")
                f.write("• Security tracking files securely deleted\n")
                f.write("• Breach event logged\n")
                f.write("• Application terminated\n\n")
                f.write("Contact the document author to report this incident.\n")
        except Exception as e:
            print(f"Error creating breach report: {e}")
    
    def _log_security_event(self, event_type):
        """Log security events"""
        if not self.config.get('log_attempts', True):
            return
            
        try:
            log_file = Path.home() / ".pdf_security" / "security.log"
            log_file.parent.mkdir(exist_ok=True)
            
            with open(log_file, 'a') as f:
                f.write(f"{datetime.now().isoformat()}: {event_type} - {os.path.basename(self.pdf_path)} - Attempts: {self.attempt_count}\n")
        except Exception as e:
            print(f"Error logging security event: {e}")
    
    def authenticate(self, password):
        """Authenticate user with password"""
        # Generate expected passwords (same logic as your existing tools)
        user_password, owner_password = self._generate_passwords()
        
        if password in [user_password, owner_password]:
            self.is_authenticated = True
            self._log_security_event("SUCCESSFUL_LOGIN")
            return True
        else:
            self.attempt_count += 1
            self.save_security_data()
            self._log_security_event(f"FAILED_ATTEMPT_{self.attempt_count}")
            
            if self.attempt_count >= self.max_attempts:
                self._trigger_self_destruct()
                return False
            else:
                remaining = self.max_attempts - self.attempt_count
                messagebox.showwarning(
                    "Authentication Failed",
                    f"Incorrect password!\n\n"
                    f"Remaining attempts: {remaining}\n"
                    f"Warning: After {self.max_attempts} failed attempts, "
                    "this document will be permanently inaccessible."
                )
                return False
    
    def _generate_passwords(self):
        """Generate passwords using the same logic as existing tools"""
        base_name = os.path.splitext(os.path.basename(self.pdf_path))[0]
        year = datetime.now().year
        
        # User password
        user_password = f"Print{year}Only"
        
        # Owner password
        author = self.config.get('author', 'Document Author')
        base_string = f"{author}_{base_name}_{datetime.now().strftime('%Y%m%d')}"
        owner_password = hashlib.md5(base_string.encode()).hexdigest()[:16]
        
        return user_password, owner_password
    
    def show_login_dialog(self):
        """Show login dialog"""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide main window
        
        # Show security warning
        messagebox.showinfo(
            "Secure Document Access",
            f"This is a protected document by {self.config.get('author', 'Unknown Author')}.\n\n"
            f"⚠️  Warning: You have {self.max_attempts - self.attempt_count} attempt(s) remaining.\n"
            "After maximum attempts, access will be permanently disabled.\n\n"
            "Enter the correct password to proceed."
        )
        
        password = simpledialog.askstring(
            "Document Password",
            "Enter password to access this document:",
            show='*'
        )
        
        if password is None:
            messagebox.showinfo("Access Cancelled", "Document access cancelled by user.")
            sys.exit(0)
        
        return self.authenticate(password)
    
    def open_document(self):
        """Open and display the PDF document"""
        if not self.is_authenticated:
            if not self.show_login_dialog():
                return False
        
        try:
            # Create a simple PDF viewer window
            self.root.deiconify()  # Show main window
            self.root.title(f"Secure PDF Viewer - {os.path.basename(self.pdf_path)}")
            self.root.geometry("800x600")
            
            # Add security watermark
            info_frame = tk.Frame(self.root, bg='red', height=30)
            info_frame.pack(fill='x')
            tk.Label(
                info_frame, 
                text="🔒 PROTECTED DOCUMENT - AUTHORIZED ACCESS ONLY 🔒",
                bg='red', fg='white', font=('Arial', 12, 'bold')
            ).pack()
            
            # Add document info
            tk.Label(
                self.root,
                text=f"Document: {os.path.basename(self.pdf_path)}\n"
                     f"Author: {self.config.get('author', 'Unknown')}\n"
                     f"Opened: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                font=('Arial', 10)
            ).pack(pady=10)
            
            # Add instructions
            tk.Label(
                self.root,
                text="This document is for viewing and printing only.\n"
                     "Copying, modifying, or redistributing is prohibited.",
                font=('Arial', 9),
                fg='red'
            ).pack(pady=5)
            
            # Show passwords for legitimate user
            user_pwd, owner_pwd = self._generate_passwords()
            tk.Label(
                self.root,
                text=f"User Password: {user_pwd}\n"
                     f"Owner Password: {owner_pwd[:8]}...",
                font=('Arial', 8),
                fg='gray'
            ).pack(pady=5)
            
            # Add close button
            tk.Button(
                self.root,
                text="Close Document",
                command=self._close_application,
                bg='lightcoral',
                font=('Arial', 12)
            ).pack(pady=20)
            
            # Log successful access
            self._log_security_event("DOCUMENT_ACCESSED")
            
            self.root.mainloop()
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Error opening document: {e}")
            return False
    
    def _close_application(self):
        """Safely close the application"""
        self._log_security_event("DOCUMENT_CLOSED")
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        self.root.quit()
        sys.exit(0)

class SecurePDFCreator:
    """Create secure PDFs with embedded self-destruct viewer"""
    
    def __init__(self, author_name="Document Author", document_title="Secure Document"):
        self.author_name = author_name
        self.document_title = document_title
    
    def create_secure_executable(self, pdf_path, output_path=None):
        """Create a self-contained secure PDF executable"""
        if output_path is None:
            base_name = os.path.splitext(pdf_path)[0]
            output_path = f"{base_name}_SECURE_VIEWER.py"
        
        # Create security configuration
        config = {
            "author": self.author_name,
            "title": self.document_title,
            "max_attempts": 3,
            "lockout_hours": 24,
            "auto_destruct": True,
            "log_attempts": True,
            "require_network": False
        }
        
        # Create the secure viewer script
        with open(output_path, 'w') as f:
            f.write(f'''#!/usr/bin/env python3
"""
Secure PDF Viewer - Self-Destruct Enabled
Generated for: {self.document_title}
Author: {self.author_name}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This executable contains a password-protected PDF with self-destruct capabilities.
"""

import sys
import os

# Embedded configuration
SECURITY_CONFIG = {config}
PDF_PATH = "{pdf_path}"

# Import the SecurePDFViewer class (you would need to bundle this)
# For now, we'll just create instructions

def main():
    print("🔒 Secure PDF Viewer - Self-Destruct Enabled")
    print("=" * 50)
    print(f"Document: {SECURITY_CONFIG['title']}")
    print(f"Author: {SECURITY_CONFIG['author']}")
    print()
    print("⚠️  WARNING: This document has self-destruct capabilities!")
    print(f"• Maximum {SECURITY_CONFIG['max_attempts']} password attempts allowed")
    print(f"• Failed attempts trigger {SECURITY_CONFIG['lockout_hours']}h lockout")
    print("• Repeated failures result in permanent access loss")
    print()
    print("To run this secure viewer, you need:")
    print("1. Python 3.7+")
    print("2. Required packages: tkinter, PyPDF2, PyMuPDF")
    print("3. The SecurePDFViewer class from advanced_pdf_security.py")
    print()
    print("Contact the document author for assistance.")

if __name__ == "__main__":
    main()
''')
        
        print(f"✅ Secure viewer created: {output_path}")
        return output_path

def main():
    """Main function to run the secure PDF viewer"""
    # Check if we're being called as a module
    if len(sys.argv) < 2:
        print("🔒 Advanced PDF Security Tool with Self-Destruct")
        print("=" * 50)
        print("Usage: python advanced_pdf_security.py <pdf_file> [author_name]")
        print("\nFeatures:")
        print("• Password protection with attempt tracking")
        print("• Self-destruct after 3 failed attempts")
        print("• 24-hour lockout mechanism")
        print("• Secure logging and breach reporting")
        print("• Compatible with existing PDF security tools")
        print("\nExample:")
        print("  python advanced_pdf_security.py main.pdf \"Abdelhalim Serhani\"")
        return
    
    pdf_file = sys.argv[1]
    author_name = sys.argv[2] if len(sys.argv) > 2 else "Document Author"
    
    if not os.path.exists(pdf_file):
        print(f"❌ Error: PDF file '{pdf_file}' not found!")
        sys.exit(1)
    
    config = {
        "author": author_name,
        "title": os.path.splitext(os.path.basename(pdf_file))[0],
        "max_attempts": 3,
        "lockout_hours": 24,
        "auto_destruct": True,
        "log_attempts": True
    }
    
    print(f"🔒 Starting secure viewer for: {os.path.basename(pdf_file)}")
    print(f"👤 Author: {author_name}")
    print("⚠️  WARNING: Self-destruct enabled after 3 failed attempts!")
    print("🔑 Passwords compatible with your existing security tools")
    print()
    
    try:
        viewer = SecurePDFViewer(pdf_file, config)
        viewer.open_document()
    except Exception as e:
        print(f"❌ Error initializing secure viewer: {e}")
        print("💡 Make sure you have tkinter installed: pip install tk")

def secure_pdf_direct(pdf_path, author_name="Document Author"):
    """Direct function to secure a PDF without command line arguments"""
    config = {
        "author": author_name,
        "title": os.path.splitext(os.path.basename(pdf_path))[0],
        "max_attempts": 3,
        "lockout_hours": 24,
        "auto_destruct": True,
        "log_attempts": True
    }
    
    print(f"🔒 Initializing secure viewer for: {os.path.basename(pdf_path)}")
    print(f"👤 Author: {author_name}")
    print("⚠️  Self-destruct enabled after 3 failed attempts!")
    
    viewer = SecurePDFViewer(pdf_path, config)
    return viewer

if __name__ == "__main__":
    main() 