# 🔒 Advanced PDF Security Tool with Self-Destruct

A sophisticated PDF security system that provides **password protection** with **automatic self-destruct capabilities** after failed authentication attempts.

## ⚠️ **CRITICAL WARNING** ⚠️

**This tool includes PERMANENT self-destruct functionality!**

- After **3 failed password attempts**, the document becomes **permanently inaccessible**
- The system creates a **24-hour lockout** period before triggering final destruction
- **Security tracking files are permanently deleted** during self-destruct
- **There is NO recovery mechanism** - use with extreme caution!

## 🚀 Features

### 🛡️ Security Features
- **Password Protection**: Uses same password generation as your existing tools
- **Attempt Tracking**: Monitors and logs all access attempts
- **Self-Destruct**: Permanent document destruction after 3 failed attempts
- **Lockout Mechanism**: 24-hour cooling period before final destruction
- **Secure Logging**: Hidden security logs with attempt tracking
- **Breach Reporting**: Automatic generation of security incident reports

### 🔐 Authentication System
- **Compatible Passwords**: Works with `simple_secure_pdf.py` and `universal_pdf_security.py`
- **Dual Password Support**: User password and owner password
- **Real-time Validation**: Immediate feedback on authentication attempts
- **Progressive Warnings**: Clear indication of remaining attempts

### 📊 Monitoring & Reporting
- **Hidden Security Files**: Stores attempt data in `~/.pdf_security/`
- **Automatic Logging**: All events logged with timestamps
- **Breach Reports**: Desktop reports generated during security incidents
- **Computer Identification**: Tracks machine and user information

## 📋 Requirements

```bash
pip install PyPDF2
```

**System Requirements:**
- Python 3.7+
- Windows/Linux/macOS
- Tkinter (usually included with Python)
- PyPDF2 library

## 🚀 Usage

### Basic Usage
```bash
python advanced_pdf_security.py your_document.pdf "Your Name"
```

### Example with Your Thesis
```bash
python advanced_pdf_security.py "main_SECURE_FOR_PRINTING.pdf" "Abdelhalim Serhani"
```

## 🔑 Password Compatibility

This tool uses the **same password generation logic** as your existing security tools:

- **User Password**: `Print2025Only` (or current year)
- **Owner Password**: MD5 hash based on author name and document

The passwords are **automatically compatible** with documents secured using:
- `simple_secure_pdf.py`
- `universal_pdf_security.py`

## ⚡ Self-Destruct Sequence

### Phase 1: Failed Attempts (1-2 attempts)
- ⚠️ Warning dialog with remaining attempts
- 🔄 Security data updated
- 📝 Failed attempt logged

### Phase 2: Critical Warning (3rd attempt)
- 🚨 **FINAL WARNING** dialog
- ⏰ Last chance notification
- 📊 Attempt count at maximum

### Phase 3: Self-Destruct Triggered
1. 💥 **Security breach detected** dialog
2. 🗑️ **Secure deletion** of tracking files
3. 📄 **Breach report** created on desktop
4. 🔒 **Permanent access termination**
5. ❌ **Application exit**

## 📁 File Locations

### Security Files (Hidden)
```
~/.pdf_security/
├── [document_hash].sec     # Attempt tracking data
└── security.log            # Complete security log
```

### Breach Reports (Desktop)
```
~/Desktop/SECURITY_BREACH_REPORT_YYYYMMDD_HHMMSS.txt
```

## 🔧 Configuration Options

The security system can be customized by modifying the config dictionary:

```python
config = {
    "author": "Your Name",
    "title": "Document Title", 
    "max_attempts": 3,          # Maximum failed attempts
    "lockout_hours": 24,        # Lockout duration
    "auto_destruct": True,      # Enable self-destruct
    "log_attempts": True,       # Enable logging
    "require_network": False    # Network requirement
}
```

## 🔐 Security Architecture

### Password Generation
```python
# User password (for printing)
user_password = f"Print{year}Only"

# Owner password (full control)
owner_password = hashlib.md5(f"{author}_{filename}_{date}").hexdigest()[:16]
```

### Attempt Tracking
- Each document gets a **unique security file** based on PDF hash
- Failed attempts are **immediately saved** to disk
- **Lockout periods** prevent brute force attacks
- **Secure deletion** overwrites files with random data

### Self-Destruct Process
1. **Detection**: 3rd failed attempt triggers sequence
2. **Warning**: User notified of security breach
3. **Logging**: Final security event recorded
4. **Deletion**: Security files securely overwritten
5. **Reporting**: Breach report created for audit trail
6. **Termination**: Application permanently disabled

## 🚨 Emergency Procedures

### If Self-Destruct is Accidentally Triggered
1. **Check Desktop** for breach report file
2. **Look in security logs** at `~/.pdf_security/security.log`
3. **Contact document author** with incident details
4. **NO RECOVERY IS POSSIBLE** - document access is permanently lost

### Preventing Accidental Destruction
1. **Test passwords** with a copy first
2. **Keep password records** secure and accessible
3. **Warn users** about the self-destruct feature
4. **Consider disabling** `auto_destruct` for testing

## 📞 Integration with Existing Tools

This tool is designed to work seamlessly with your existing PDF security infrastructure:

### Compatible with simple_secure_pdf.py
```bash
# Secure the PDF first
python simple_secure_pdf.py original.pdf secured.pdf

# Add self-destruct protection
python advanced_pdf_security.py secured.pdf "Abdelhalim Serhani"
```

### Compatible with universal_pdf_security.py
```bash
# Batch secure PDFs
python universal_pdf_security.py *.pdf

# Add self-destruct to specific files
python advanced_pdf_security.py document_SECURE.pdf "Author Name"
```

## 🎯 Use Cases

### 🎓 **Thesis Protection** (Your Use Case)
- Secure thesis for printing services
- Prevent unauthorized copying or distribution
- Ensure document destruction if compromised
- Track access attempts by print shop

### 📊 **Confidential Reports**
- Financial documents with sensitive data
- Legal documents requiring destruction
- Corporate reports with trade secrets
- Medical records with privacy requirements

### 🏛️ **Government Documents**
- Classified material with security requirements
- Sensitive diplomatic documents
- Intelligence reports requiring auto-destruction
- Audit documents with compliance needs

## ⚖️ Legal and Ethical Considerations

### 📋 Important Warnings
- **Inform users** about self-destruct capabilities
- **Obtain consent** before deploying destructive security
- **Consider legal implications** of permanent data destruction
- **Maintain audit trails** for compliance requirements

### 🛡️ Recommended Practices
1. **Test thoroughly** before production use
2. **Document security policies** clearly
3. **Train users** on proper password handling
4. **Backup critical documents** before securing
5. **Consider compliance** with data protection laws

## 🔍 Troubleshooting

### Common Issues

**Issue**: "tkinter not found"
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
```

**Issue**: "PDF file not found"
- Check file path and permissions
- Ensure PDF is not corrupted
- Verify file extension is `.pdf`

**Issue**: "Security file permission denied"
- Check home directory permissions
- Ensure `.pdf_security` folder is writable
- Run with appropriate user privileges

### Recovery Options

**Forgotten Password**:
- Check your password generation logic
- Look in instruction files from original tools
- Contact original document creator

**Accidental Lockout**:
- Wait 24 hours for automatic reset
- Check system clock for time accuracy
- Delete security files manually (advanced users only)

## 📈 Security Metrics

The system tracks comprehensive security metrics:

- **Total access attempts**
- **Failed authentication count**
- **Time between attempts**
- **User and machine identification**
- **Document access duration**
- **Security policy violations**

## 🤝 Support and Contact

For issues with this security tool:

1. **Check logs** in `~/.pdf_security/security.log`
2. **Review breach reports** on desktop
3. **Test with sample documents** first
4. **Contact the document author** for password issues

---

## ⚠️ **FINAL WARNING** ⚠️

**This tool will PERMANENTLY destroy document access after 3 failed attempts.**

**Use with extreme caution and ensure users are properly informed!**

**No recovery mechanism exists - lost passwords mean lost access forever!**

---

*Developed for enhanced document security with military-grade protection standards.* 