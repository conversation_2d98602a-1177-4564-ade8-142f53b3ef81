#!/usr/bin/env python3
"""
Test script to validate the fixes made to report_service.py
"""

def test_python_syntax():
    """Test that the Python file has valid syntax"""
    import ast
    
    report_service_path = "/home/<USER>/Documents/repos/flet/Hiel-RnE-Model-v4/services/report_service.py"
    
    try:
        with open(report_service_path, 'r') as f:
            content = f.read()
        
        # Parse the file to check for syntax errors
        ast.parse(content)
        print("✓ report_service.py has valid Python syntax")
        
        # Check for the specific patterns we fixed
        fixed_patterns = [
            "chart_result = self.chart_factory.create_dcf_waterfall_chart(",
            "chart_results = self.chart_factory.create_enhanced_monte_carlo_dashboard(",
            "chart_result = self.chart_factory.create_tornado_diagram(",
            "chart_results = self.chart_factory.create_risk_dashboard(",
            "chart_results = self.chart_factory.create_market_analysis_dashboard(",
            "chart_result = self.chart_factory.create_lcoe_incentive_waterfall(",
        ]
        
        missing_patterns = []
        for pattern in fixed_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print("✗ Some fixes may not have been applied:")
            for pattern in missing_patterns:
                print(f"  - Missing: {pattern}")
        else:
            print("✓ All chart unpacking fixes appear to be applied")
            
        # Check that we removed the problematic patterns
        problematic_patterns = [
            "_, chart_bytes = self.chart_factory.create_dcf_waterfall_chart(",
            "_, chart_bytes = self.chart_factory.create_enhanced_monte_carlo_dashboard(",
            "_, chart_bytes = self.chart_factory.create_tornado_diagram(",
            "_, chart_bytes = self.chart_factory.create_risk_dashboard(",
            "_, chart_bytes = self.chart_factory.create_market_analysis_dashboard(",
            "_, chart_bytes = self.chart_factory.create_lcoe_incentive_waterfall(",
        ]
        
        remaining_problems = []
        for pattern in problematic_patterns:
            if pattern in content:
                remaining_problems.append(pattern)
        
        if remaining_problems:
            print("✗ Some problematic patterns still remain:")
            for pattern in remaining_problems:
                print(f"  - Found: {pattern}")
        else:
            print("✓ All problematic unpacking patterns have been removed")
            
        return len(missing_patterns) == 0 and len(remaining_problems) == 0
        
    except SyntaxError as e:
        print(f"✗ Syntax error in report_service.py: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading report_service.py: {e}")
        return False

if __name__ == "__main__":
    success = test_python_syntax()
    if success:
        print("\n🎉 All fixes have been successfully applied!")
        print("The chart generation errors should now be resolved.")
    else:
        print("\n❌ Some issues remain that need to be addressed.")