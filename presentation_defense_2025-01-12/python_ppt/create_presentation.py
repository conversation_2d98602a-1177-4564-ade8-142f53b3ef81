#!/usr/bin/env python3
"""
Script to generate PowerPoint presentation for thesis defense
Piano <PERSON>ei - Italian Investments in Morocco

Author: <PERSON><PERSON><PERSON><PERSON> SERHANI
Date: 2025-01-12
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.enum.dml import MSO_THEME_COLOR
import os

def add_logos_to_slide(slide, logo_left=None, logo_right=None):
    """Add logos to top corners of slide"""
    try:
        if logo_left and os.path.exists(logo_left):
            slide.shapes.add_picture(logo_left, <PERSON>hes(0.2), <PERSON><PERSON>(0.1), <PERSON><PERSON>(1), <PERSON>hes(0.5))
    except:
        pass
    try:
        if logo_right and os.path.exists(logo_right):
            slide.shapes.add_picture(logo_right, Inches(8.8), <PERSON><PERSON>(0.1), <PERSON><PERSON>(1), <PERSON><PERSON>(0.5))
    except:
        pass

def create_defense_presentation():
    """Create the complete defense presentation"""
    
    # Create presentation object
    prs = Presentation()
    
    # Define colors
    main_color = RGBColor(0, 51, 102)  # Dark blue
    accent_color = RGBColor(204, 102, 0)  # Orange
    
    # Logo paths
    logo_left = "../../figures/university_logo.png"
    logo_right = "../../figures/agevolami_logo.png"
    
    # Slide 1: Title slide
    slide1 = prs.slides.add_slide(prs.slide_layouts[0])  # Title slide layout
    title = slide1.shapes.title
    subtitle = slide1.placeholders[1]
    
    # Add logos to title slide
    add_logos_to_slide(slide1, logo_left, logo_right)
    
    title.text = "Fondements Stratégiques et Juridiques des Investissements Italiens au Maroc dans le Cadre du Piano Mattei"
    title.text_frame.paragraphs[0].font.color.rgb = main_color
    title.text_frame.paragraphs[0].font.size = Pt(28)
    title.text_frame.paragraphs[0].font.name = "Times New Roman"
    
    subtitle.text = """Soutenance de Mémoire de Master

Présenté par: Abdelhalim SERHANI
Directeur de mémoire: [Nom du directeur]
Établissement d'accueil: Agevolami.it (Vérone, Italie)

Soutenu le 14 Juillet 2025

Devant le jury composé de:
Président: M. El Mehdi MAJIDI (Encadrant pédagogique)
Rapporteur 1: [Nom du rapporteur 1]
Rapporteur 2: [Nom du rapporteur 2]
Examinateur: [Nom de l'examinateur]"""
    
    # Set subtitle font
    for paragraph in subtitle.text_frame.paragraphs:
        paragraph.font.name = "Times New Roman"
        paragraph.font.size = Pt(16)
    
    # Slide 2: Plan de la présentation
    slide2 = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content
    add_logos_to_slide(slide2, logo_left, logo_right)
    slide2.shapes.title.text = "Plan de la Présentation"
    
    content = slide2.placeholders[1].text_frame
    content.text = """1. Introduction et choix du sujet
2. Problématique et hypothèses de recherche
3. Méthodologie de recherche
4. Établissement d'accueil : Agevolami.it
5. Résultats principaux
6. Apports et contributions
7. Limites et perspectives
8. Conclusion"""
    
    # Slide 3: Contexte géostratégique (Enhanced Visual)
    slide3 = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout for custom design
    add_logos_to_slide(slide3, logo_left, logo_right)
    
    # Title
    title_box = slide3.shapes.add_textbox(Inches(1), Inches(0.8), Inches(8), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "🌍 1. Contexte Géostratégique"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = main_color
    title_frame.paragraphs[0].font.name = "Times New Roman"
    
    # Create colored boxes for key info
    # Piano Mattei box
    box1 = slide3.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(1), Inches(2), Inches(3.5), Inches(1.5))
    box1.fill.solid()
    box1.fill.fore_color.rgb = RGBColor(230, 245, 255)  # Light blue
    box1.line.color.rgb = main_color
    box1.line.width = Pt(2)
    
    text1 = box1.text_frame
    text1.text = "💰 Piano Mattei\n5,5 milliards € mobilisés\n(2024-2028)"
    text1.paragraphs[0].font.size = Pt(16)
    text1.paragraphs[0].font.bold = True
    text1.paragraphs[0].font.name = "Times New Roman"
    
    # Maroc 2030 box
    box2 = slide3.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(5.5), Inches(2), Inches(3.5), Inches(1.5))
    box2.fill.solid()
    box2.fill.fore_color.rgb = RGBColor(255, 245, 230)  # Light orange
    box2.line.color.rgb = accent_color
    box2.line.width = Pt(2)
    
    text2 = box2.text_frame
    text2.text = "🔆 Maroc 2030\n52% d'énergies renouvelables\ndans le mix électrique"
    text2.paragraphs[0].font.size = Pt(16)
    text2.paragraphs[0].font.bold = True
    text2.paragraphs[0].font.name = "Times New Roman"
    
    # Convergence section
    conv_box = slide3.shapes.add_textbox(Inches(1), Inches(4), Inches(8), Inches(2))
    conv_frame = conv_box.text_frame
    conv_frame.text = """🤝 Convergence d'intérêts:
🏭 Expertise technologique italienne
☀️ Ressources naturelles marocaines exceptionnelles
🌊 Complémentarité géostratégique méditerranéenne"""
    for paragraph in conv_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.name = "Times New Roman"
    
    # Add image 
    try:
        img_path = "../../figures/executive_summary_dashboard.png"
        if os.path.exists(img_path):
            slide3.shapes.add_picture(img_path, Inches(7), Inches(1.5), Inches(3.5), Inches(3.5))
    except:
        pass
    
    # Slide 4: Justification du choix
    slide4 = prs.slides.add_slide(prs.slide_layouts[1])
    slide4.shapes.title.text = "Justification du Choix du Sujet"
    
    content = slide4.placeholders[1].text_frame
    content.text = """Gap Académique Identifié:
Aucune étude quantifiée des synergies Piano Mattei-Maroc

Opportunité Pratique:
Stage de 3 mois chez Agevolami.it (février-mai 2025)

Enjeux Économiques:
Optimisation des investissements pour les PME italiennes

Timing Stratégique:
Convergence temporelle unique Piano Mattei + Stratégie Maroc 2030"""
    
    # Slide 5: Problématique centrale
    slide5 = prs.slides.add_slide(prs.slide_layouts[1])
    slide5.shapes.title.text = "2. Problématique Centrale"
    
    content = slide5.placeholders[1].text_frame
    p = content.paragraphs[0]
    p.text = "Dans quelle mesure l'intégration des dispositifs du Piano Mattei avec les incitations marocaines peut-elle optimiser la structure financière et la rentabilité des projets d'énergies renouvelables italiens au Maroc ?"
    p.font.size = Pt(24)
    p.font.color.rgb = main_color
    p.font.bold = True
    
    # Slide 6: Hypothèses de recherche
    slide6 = prs.slides.add_slide(prs.slide_layouts[1])
    slide6.shapes.title.text = "Hypothèses de Recherche"
    
    content = slide6.placeholders[1].text_frame
    content.text = """H1: Les incitations MASEN/IRESEN augmentent le TRI 
     d'au moins 5 points de pourcentage

H2: Le financement croisé italo-marocain réduit le LCOE 
     d'au moins 10%

H3: La localisation à Dakhla génère un TRI supérieur 
     à Ouarzazate

Méthode de Validation:
Test empirique via modélisation financière comparative (Hiel RnE v3.0)"""
    
    # Slide 7: Méthodologie (Enhanced)
    slide7 = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    add_logos_to_slide(slide7, logo_left, logo_right)
    
    # Title
    title_box = slide7.shapes.add_textbox(Inches(1), Inches(0.5), Inches(8), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "3. Méthodologie de Recherche"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = main_color
    title_frame.paragraphs[0].font.name = "Times New Roman"
    
    # Create 4 methodology boxes
    box_width = Inches(4.5)
    box_height = Inches(2.2)
    
    # Box 1: Analyse Documentaire
    box1 = slide7.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(0.5), Inches(2), box_width, box_height)
    box1.fill.solid()
    box1.fill.fore_color.rgb = RGBColor(230, 245, 255)  # Light blue
    box1.line.color.rgb = main_color
    box1.line.width = Pt(2)
    
    text1 = box1.text_frame
    text1.text = "• 1. Analyse Documentaire\n• Sources Piano Mattei\n• Cadre juridique marocain\n• Conventions fiscales"
    for p in text1.paragraphs:
        p.font.size = Pt(14)  # Reduced font size
        p.font.name = "Times New Roman"
    
    # Box 2: Étude de Cas
    box2 = slide7.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(5.5), Inches(2), box_width, box_height)
    box2.fill.solid()
    box2.fill.fore_color.rgb = RGBColor(255, 245, 230)  # Light orange
    box2.line.color.rgb = accent_color
    box2.line.width = Pt(2)
    
    text2 = box2.text_frame
    text2.text = "• 2. Étude de Cas\n• Projet solaire 10 MW\n• Localisation: Ouarzazate\n• Paramètres réalistes"
    for p in text2.paragraphs:
        p.font.size = Pt(14)  # Reduced font size
        p.font.name = "Times New Roman"
    
    # Box 3: Modélisation
    box3 = slide7.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(0.5), Inches(4.5), box_width, box_height)
    box3.fill.solid()
    box3.fill.fore_color.rgb = RGBColor(230, 245, 255)  # Light blue
    box3.line.color.rgb = main_color
    box3.line.width = Pt(2)
    
    text3 = box3.text_frame
    text3.text = "• 3. Modélisation Financière\n• Hiel RnE v3.0 (Python)\n• Monte-Carlo\n• Benchmarking"
    for p in text3.paragraphs:
        p.font.size = Pt(14)  # Reduced font size
        p.font.name = "Times New Roman"
    
    # Box 4: Expérience
    box4 = slide7.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(5.5), Inches(4.5), box_width, box_height)
    box4.fill.solid()
    box4.fill.fore_color.rgb = RGBColor(255, 245, 230)  # Light orange
    box4.line.color.rgb = accent_color
    box4.line.width = Pt(2)
    
    text4 = box4.text_frame
    text4.text = "• 4. Expérience Terrain\n• 3 mois chez Agevolami.it\n• Projets clients réels\n• Audit et conformité"
    for p in text4.paragraphs:
        p.font.size = Pt(14)  # Reduced font size
        p.font.name = "Times New Roman"
    
    # Slide 8: Innovation méthodologique
    slide8 = prs.slides.add_slide(prs.slide_layouts[1])
    slide8.shapes.title.text = "Innovation: Hiel RnE Modeler v3.0"
    
    content = slide8.placeholders[1].text_frame
    content.text = """Caractéristiques techniques:
• Développé en Python + Flet framework
• Interface graphique intuitive
• Simulation Monte-Carlo intégrée
• Validation croisée automatique

Fonctionnalités spécialisées:
• Gestion subventions croisées italo-marocaines
• Optimisation fiscale transfrontalière
• Reporting multi-format professionnel
• Benchmarking avec standards IRENA"""
    
    # Add architecture image
    try:
        img_path = "../../figures/architecture_hiel_rne_modeler.png"
        if os.path.exists(img_path):
            slide8.shapes.add_picture(img_path, Inches(7), Inches(2), Inches(3), Inches(3))
    except:
        pass
    
    # Slide 9: Agevolami.it présentation
    slide9 = prs.slides.add_slide(prs.slide_layouts[1])
    slide9.shapes.title.text = "4. Établissement d'Accueil: Agevolami.it"
    
    content = slide9.placeholders[1].text_frame
    content.text = """Profil de l'entreprise:
• Fondation: 2021, Vérone (Italie)
• Spécialisation: Ingénierie fiscale et financière
• Cible: PME énergies renouvelables
• Expertise: Piano Mattei + internationalisation

Positionnement stratégique:
• Interface écosystèmes italien-marocain
• Accompagnement projets transfrontaliers
• Optimisation fiscale et financière
• Réseau institutionnel privilégié"""
    
    # Slide 10: Missions réalisées
    slide10 = prs.slides.add_slide(prs.slide_layouts[1])
    slide10.shapes.title.text = "Missions Réalisées Durant le Stage"
    
    content = slide10.placeholders[1].text_frame
    content.text = """Consulting projets incitatifs italiens:
• ZES Unica 2025, Autoproduzione energia PMI
• Investimenti Sostenibili 4.0, Accordi Innovazione
• Préparation dossiers, guidelines, rapports

Audit et conformité:
• Audit 1er SAL projet client existant
• Monitoring conformité timesheets
• Validation documentaire et processus

Innovation technique:
• Développement Hiel RnE Modeler v3.0
• Tests et validation sur projets réels"""
    
    # Slide 11: Cas d'étude
    slide11 = prs.slides.add_slide(prs.slide_layouts[1])
    slide11.shapes.title.text = "5. Cas d'Étude: Projet Solaire 10 MW"
    
    content = slide11.placeholders[1].text_frame
    content.text = """Caractéristiques techniques:
• Localisation: Ouarzazate, Maroc
• Capacité: 10 MW photovoltaïque
• Technologie: Panneaux bifaciaux + trackers
• CAPEX: 8,5 millions €
• Production: 17 GWh/an

Objectifs:
• Test des hypothèses H1, H2, H3
• Validation modèle Hiel RnE v3.0
• Démonstration synergies pratiques"""
    
    # Add KPI dashboard image
    try:
        img_path = "../../figures/financial_kpis_dashboard.png"
        if os.path.exists(img_path):
            slide11.shapes.add_picture(img_path, Inches(7), Inches(2), Inches(3), Inches(3))
    except:
        pass
    
    # Slide 12: Performance financière (Enhanced)
    slide12 = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    add_logos_to_slide(slide12, logo_left, logo_right)
    
    # Title
    title_box = slide12.shapes.add_textbox(Inches(1), Inches(0.5), Inches(8), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "Performance Financière Exceptionnelle"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = main_color
    title_frame.paragraphs[0].font.name = "Times New Roman"
    
    # Performance Box
    perf_box = slide12.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(1), Inches(1.5), Inches(8), Inches(3.5))
    perf_box.fill.solid()
    perf_box.fill.fore_color.rgb = RGBColor(230, 255, 230)  # Light green
    perf_box.line.color.rgb = RGBColor(0, 128, 0)  # Green border
    perf_box.line.width = Pt(3)
    
    perf_text = perf_box.text_frame
    perf_text.text = """↑ TRI projet: 18,1% (excellente performance)
% TRI fonds propres: 15,0% (seuil atteint)
€ VAN projet: 6,13 M€ sur 25 ans
~ LCOE optimisé: 0,062 €/kWh
↓ Réduction LCOE: -20,5%

✓ Toutes les métriques dépassent les seuils"""
    
    for paragraph in perf_text.paragraphs:
        paragraph.font.size = Pt(16)  # Reduced font size
        paragraph.font.name = "Times New Roman"
        paragraph.font.bold = True
    
    # Slide 13: Validation hypothèses
    slide13 = prs.slides.add_slide(prs.slide_layouts[1])
    slide13.shapes.title.text = "Validation des Hypothèses"
    
    content = slide13.placeholders[1].text_frame
    content.text = """H1 ✓ CONFIRMÉE:
Réduction LCOE 20,5% + amélioration TRI significative 
grâce aux incitations MASEN/IRESEN

H2 ✓ CONFIRMÉE:
Réduction LCOE 28,2% (dépassant largement l'objectif 10%) 
via financement croisé

H3 ✓ CONFIRMÉE:
TRI fonds propres 15,0% + VAN positive 5,27 M€ 
démontrent l'attractivité d'investissement

TOUTES LES HYPOTHÈSES VALIDÉES 
AVEC PREUVES QUANTITATIVES"""
    
    # Slide 14: Structure incitations
    slide14 = prs.slides.add_slide(prs.slide_layouts[1])
    slide14.shapes.title.text = "Structure d'Incitations Optimisée"
    
    content = slide14.placeholders[1].text_frame
    content.text = """Répartition des subventions:

Total: 37,6% du CAPEX (3,2 M€)

Incitations italiennes (19,5%):
• Piano Mattei: 13,8%
• SIMEST Fonds Afrique: 5,7%

Incitations marocaines (12,6%):
• MASEN: 9,2%
• Soutien raccordement: 3,4%

ÉQUILIBRE OPTIMAL DÉMONTRÉ"""
    
    # Slide 15: Performance Hiel RnE
    slide15 = prs.slides.add_slide(prs.slide_layouts[1])
    slide15.shapes.title.text = "Innovation Hiel RnE v3.0 - Performance"
    
    content = slide15.placeholders[1].text_frame
    content.text = """Comparaison Performance:

Temps d'analyse:
• Traditionnel: 2-3 semaines → Hiel RnE: 2-3 heures

Scénarios analysés:
• Traditionnel: 2-3 manuels → Hiel RnE: 8+ automatisés

Précision calculs:
• Traditionnel: ±2% → Hiel RnE: ±0,1%

Reproductibilité:
• Traditionnel: Limitée → Hiel RnE: Totale

GAIN D'EFFICACITÉ DE 90%"""
    
    # Slide 16: Contributions
    slide16 = prs.slides.add_slide(prs.slide_layouts[1])
    slide16.shapes.title.text = "6. Contributions de la Recherche"
    
    content = slide16.placeholders[1].text_frame
    content.text = """Contribution Scientifique:
• Premier modèle quantifié synergies Piano Mattei-Maroc
• Méthodologie reproductible pour autres pays africains
• Innovation en modélisation financière transfrontalière

Contribution Pratique:
• Outil opérationnel pour cabinets de conseil
• Guide concret pour PME italiennes
• ROI démontré: 14,5:1 (bénéfices/coûts)

Contribution Économique:
• Viabilité démontrée projets italo-marocains
• Potentiel réplication autres investissements
• Impact climatique: 10 200 tonnes CO₂ évitées/an"""
    
    # Slide 17: Limites
    slide17 = prs.slides.add_slide(prs.slide_layouts[1])
    slide17.shapes.title.text = "7. Limites de l'Étude"
    
    content = slide17.placeholders[1].text_frame
    content.text = """Limites identifiées:

• Scope géographique: Deux localisations seulement
  (Ouarzazate, Dakhla)

• Secteur: Focus photovoltaïque 
  (éolien, hydrogène exclus)

• Temporalité: Projections 25 ans 
  (évolutions réglementaires imprévisibles)

• Validation empirique: Cas hypothétique 
  (besoin validation sur projets réels)

Considérations méthodologiques:
• Hypothèses macroéconomiques stables
• Réglementation actuelle (fin 2024)"""
    
    # Slide 18: Perspectives
    slide18 = prs.slides.add_slide(prs.slide_layouts[1])
    slide18.shapes.title.text = "Perspectives Futures"
    
    content = slide18.placeholders[1].text_frame
    content.text = """Développements sectoriels:
• Hydrogène vert: Feuille de route 2024-2035
• Stockage d'énergie: Intégration batteries
• Réseaux intelligents: Digitalisation

Extensions géographiques:
• Autres pays Piano Mattei (Tunisie, Algérie)
• Adaptation modèle autres contextes
• Partenariats européens élargis

Développements techniques:
• Plateforme SaaS Hiel RnE
• Intelligence artificielle prédictive
• Blockchain pour traçabilité

Coopération institutionnelle:
• Portail commun italo-marocain
• Harmonisation réglementaire"""
    
    # Slide 19: Conclusion
    slide19 = prs.slides.add_slide(prs.slide_layouts[1])
    slide19.shapes.title.text = "8. Conclusion"
    
    content = slide19.placeholders[1].text_frame
    content.text = """Problématique résolue avec preuves quantifiées:
• Intégration efficace: 37,6% subventions totales
• Optimisation financière: TRI 18,1%, LCOE -20,5%
• Synergies validées: Complémentarité démontrée

Outils développés:
• Hiel RnE Modeler v3.0: Innovation méthodologique
• Recommandations opérationnelles pour chaque acteur
• Feuille de route hydrogène vert

Impact et perspectives:
• Nouveau modèle coopération euro-africaine
• Outils pratiques investissements transfrontaliers
• Contribution objectifs climatiques partagés"""
    
    # Slide 20: Remerciements
    slide20 = prs.slides.add_slide(prs.slide_layouts[1])
    slide20.shapes.title.text = "Remerciements"
    
    content = slide20.placeholders[1].text_frame
    content.text = """

• Directeur de mémoire pour l'encadrement scientifique rigoureux

• Équipe Agevolami.it pour l'accueil et l'accompagnement professionnel

• Institutions partenaires MASEN, SIMEST, SACE pour leur collaboration

• Jury de soutenance pour l'évaluation de ce travail



MERCI POUR VOTRE ATTENTION

Questions et discussion"""
    
    # Format all slides
    for i, slide in enumerate(prs.slides):
        # Add logos to all slides except title slide
        if i > 0:
            add_logos_to_slide(slide, logo_left, logo_right)
        
        # Format title
        if slide.shapes.title:
            slide.shapes.title.text_frame.paragraphs[0].font.color.rgb = main_color
            slide.shapes.title.text_frame.paragraphs[0].font.size = Pt(32)
            slide.shapes.title.text_frame.paragraphs[0].font.bold = True
            slide.shapes.title.text_frame.paragraphs[0].font.name = "Times New Roman"
        
        # Format content
        for shape in slide.shapes:
            if shape.has_text_frame and shape != slide.shapes.title:
                for paragraph in shape.text_frame.paragraphs:
                    paragraph.font.size = Pt(18)
                    paragraph.font.name = "Times New Roman"
                    paragraph.space_after = Pt(12)
    
    return prs

def add_slide_numbers(prs):
    """Add slide numbers to all slides except title slide"""
    for i, slide in enumerate(prs.slides):
        if i == 0:  # Skip title slide
            continue
        
        # Add slide number
        textbox = slide.shapes.add_textbox(Inches(9), Inches(7), Inches(1), Inches(0.5))
        text_frame = textbox.text_frame
        text_frame.text = f"{i}/{len(prs.slides)-1}"
        text_frame.paragraphs[0].font.size = Pt(12)
        text_frame.paragraphs[0].font.color.rgb = RGBColor(128, 128, 128)

def main():
    """Main function to create and save presentation"""
    print("Creating thesis defense presentation...")
    
    # Create presentation
    prs = create_defense_presentation()
    
    # Add slide numbers
    add_slide_numbers(prs)
    
    # Save presentation
    output_path = "presentation_defense_serhani.pptx"
    prs.save(output_path)
    
    print(f"Presentation saved as: {output_path}")
    print(f"Total slides: {len(prs.slides)}")
    print("\nSlide breakdown:")
    slide_titles = [
        "Title Slide",
        "Plan de la Présentation", 
        "Contexte Géostratégique",
        "Justification du Choix",
        "Problématique Centrale",
        "Hypothèses de Recherche",
        "Méthodologie",
        "Innovation Hiel RnE",
        "Agevolami.it",
        "Missions Réalisées",
        "Cas d'Étude",
        "Performance Financière",
        "Validation Hypothèses",
        "Structure Incitations",
        "Performance Hiel RnE",
        "Contributions",
        "Limites",
        "Perspectives",
        "Conclusion",
        "Remerciements"
    ]
    
    for i, title in enumerate(slide_titles, 1):
        print(f"  {i:2d}. {title}")

if __name__ == "__main__":
    main()