# 16:9 Layout Ratios Reference Guide

## Beamer Standard Dimensions
- **Width**: 364.19pt = 12.85cm
- **Height**: 272.94pt = 9.63cm
- **Aspect Ratio**: 1.334 (16:9 standard)

## Optimized Layout Ratios for Reuse

### 1. Standard Balanced Layout
```latex
\begin{columns}[T,totalwidth=0.95\paperwidth]
\begin{column}{0.46\paperwidth}
\begin{column}{0.46\paperwidth}
```
- **Data**: 46% (168pt) - Equal emphasis
- **Visual**: 46% (168pt) - Equal emphasis  
- **Gap**: 3% (10pt) - Clean separation
- **Use case**: Balanced presentations, equal content importance

### 2. Visual-Focused Layout
```latex
\begin{columns}[T,totalwidth=0.95\paperwidth]
\begin{column}{0.42\paperwidth}
\begin{column}{0.51\paperwidth}
```
- **Data**: 42% (153pt) - Substantial content
- **Visual**: 51% (186pt) - Prominent display
- **Gap**: 2% (7pt) - Minimal separation
- **Use case**: Image-important slides, dashboards

### 3. Image-Prominent Layout
```latex
\begin{columns}[T,totalwidth=0.95\paperwidth]
\begin{column}{0.294\paperwidth}
\begin{column}{0.636\paperwidth}
```
- **Data**: 29.4% (107pt) - Compact essentials
- **Visual**: 63.6% (232pt) - Major prominence
- **Gap**: 2% (7pt) - Clean spacing
- **Use case**: Technical demonstrations, software showcases

### 4. Maximum Visual Impact Layout
```latex
\begin{columns}[T,totalwidth=0.95\paperwidth]
\begin{column}{0.235\paperwidth}
\begin{column}{0.695\paperwidth}
```
- **Data**: 23.5% (86pt) - Ultra-compact
- **Visual**: 69.5% (253pt) - Extreme prominence
- **Gap**: 2% (7pt) - Standard spacing
- **Use case**: High-impact visuals, executive presentations

## Content Optimization Strategies

### Ultra-Compact Data Column (≤25% width)
- **Font size**: `\tiny` (6pt)
- **Spacing**: `\vspace{0.1cm}` minimal
- **Items**: 8-10 maximum
- **Descriptions**: Single words
- **Headers**: Minimal or removed

### Compact Data Column (25-35% width)
- **Font size**: `\footnotesize` (8pt)
- **Spacing**: `\vspace{0.2cm}` moderate
- **Items**: 10-12 items
- **Descriptions**: Brief phrases
- **Headers**: Short titles

### Standard Data Column (35-45% width)
- **Font size**: `\small` (10pt)
- **Spacing**: `\vspace{0.3cm}` standard
- **Items**: 12-15 items
- **Descriptions**: Full phrases
- **Headers**: Complete titles

## Image Optimization Guidelines

### Maximum Visual Impact (≥65% width)
- **Scaling**: `width=0.99\textwidth,height=0.75\textheight`
- **Caption**: Brief but descriptive
- **Border**: Minimal or none
- **Quality**: High resolution required

### Prominent Display (50-65% width)
- **Scaling**: `width=0.95\textwidth,height=0.65\textheight`
- **Caption**: Standard description
- **Border**: Standard tcolorbox
- **Quality**: Standard resolution

### Balanced Display (45-50% width)
- **Scaling**: `width=0.9\textwidth,keepaspectratio`
- **Caption**: Optional
- **Border**: Full tcolorbox styling
- **Quality**: Standard resolution

## Mathematical Verification Formula

For any layout, verify: `data_ratio + visual_ratio + gap_ratio = totalwidth_ratio`

Example:
- 0.235 + 0.695 + 0.020 = 0.95 ✓
- 85.7pt + 253.0pt + 7.3pt = 346.0pt ✓

## Space Efficiency Targets

- **Optimal range**: 65-75% content utilization
- **Calculation**: `(content_area / slide_area) × 100`
- **Content area**: `(data_width × content_height) + (image_width × image_height)`
- **Slide area**: `364.19 × 272.94 = 99,402pt²`

## Typography Hierarchy

### Font Size Progression
1. **Title**: `\normalsize` (11pt) - Section headers
2. **Content**: `\small` (10pt) - Main content
3. **Details**: `\footnotesize` (8pt) - Secondary info
4. **Compact**: `\tiny` (6pt) - Ultra-compact layouts

### Color Coding
- **Primary content**: `maincolor` (RGB 25,25,112)
- **Emphasis**: `accentcolor` (RGB 255,140,0)
- **Background**: `lightblue` (RGB 173,216,230)

## Implementation Notes

1. **Always use** `totalwidth=0.95\paperwidth` for optimal 16:9 utilization
2. **Keep gap ratio** at 2% (0.02) for clean separation
3. **Verify calculations** before implementation
4. **Test compilation** after applying ratios
5. **Consider content density** when choosing layout type

## Examples Applied

### Hiel RnE Modeler v3.0 Evolution
- **Original**: 0.42 + 0.52 = 0.94 (layout error)
- **Corrected**: 0.42 + 0.51 = 0.93 (balanced)
- **30% reduction**: 0.294 + 0.636 = 0.93 (image-prominent)
- **44% total reduction**: 0.235 + 0.695 = 0.93 (maximum visual)

Each step documented and verified for reuse on other slides requiring similar visual prominence optimization.