#!/usr/bin/env python3
"""
Self-Destruct PDF Security Demonstration
Shows how the advanced security system works in practice.

⚠️  WARNING: This is a demonstration only!
Use with test documents, not your actual thesis!
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

def create_demo_pdf():
    """Create a simple demo PDF for testing"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        demo_path = "demo_document.pdf"
        c = canvas.Canvas(demo_path, pagesize=letter)
        
        # Add content to the PDF
        c.drawString(100, 750, "DEMO SECURE DOCUMENT")
        c.drawString(100, 720, f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        c.drawString(100, 690, "This is a test document for demonstrating")
        c.drawString(100, 660, "the self-destruct security feature.")
        c.drawString(100, 630, "")
        c.drawString(100, 600, "⚠️  WARNING: After 3 failed password attempts,")
        c.drawString(100, 570, "this document will become permanently inaccessible!")
        c.drawString(100, 540, "")
        c.drawString(100, 510, "Correct password: Print2025Only")
        c.drawString(100, 480, "Test with wrong passwords to see self-destruct in action.")
        
        c.save()
        print(f"✅ Demo PDF created: {demo_path}")
        return demo_path
        
    except ImportError:
        print("⚠️  reportlab not installed. Creating a copy of existing PDF...")
        # If reportlab not available, create a simple text file instead
        demo_path = "demo_document.txt"
        with open(demo_path, 'w') as f:
            f.write("DEMO SECURE DOCUMENT\n")
            f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("This is a test document for demonstrating\n")
            f.write("the self-destruct security feature.\n")
            f.write("\n")
            f.write("⚠️  WARNING: After 3 failed password attempts,\n")
            f.write("this document will become permanently inaccessible!\n")
            f.write("\n")
            f.write("Correct password: Print2025Only\n")
            f.write("Test with wrong passwords to see self-destruct in action.\n")
        
        print(f"✅ Demo file created: {demo_path}")
        return demo_path

def simulate_security_tracking():
    """Simulate the security tracking system"""
    security_dir = Path.home() / ".pdf_security_demo"
    security_dir.mkdir(exist_ok=True)
    
    # Create a sample security log
    log_file = security_dir / "demo_security.log"
    with open(log_file, 'w') as f:
        f.write(f"{datetime.now().isoformat()}: DEMO_START - demo_document.pdf - Attempts: 0\n")
        f.write(f"{datetime.now().isoformat()}: SYSTEM_INIT - Security tracking enabled\n")
    
    print(f"📁 Demo security directory: {security_dir}")
    return security_dir

def show_demo_instructions():
    """Show demonstration instructions"""
    print("=" * 60)
    print("🔒 PDF SELF-DESTRUCT SECURITY DEMONSTRATION")
    print("=" * 60)
    print()
    print("This demonstration shows how the self-destruct feature works:")
    print()
    print("📋 TESTING SCENARIOS:")
    print("1. 🟢 Enter correct password (Print2025Only) - Access granted")
    print("2. 🟡 Enter wrong password once - Warning (2 attempts left)")
    print("3. 🟠 Enter wrong password twice - Final warning (1 attempt left)")
    print("4. 🔴 Enter wrong password thrice - SELF-DESTRUCT TRIGGERED")
    print()
    print("💥 WHAT HAPPENS DURING SELF-DESTRUCT:")
    print("• Security breach dialog appears")
    print("• All security tracking files are deleted")
    print("• Breach report created on desktop")
    print("• Document access permanently disabled")
    print("• Application terminates")
    print()
    print("⚠️  IMPORTANT SAFETY NOTES:")
    print("• This is a DEMO - it won't destroy your real documents")
    print("• Test with demo files only")
    print("• Real implementation has NO recovery mechanism")
    print("• Always backup important documents before securing")
    print()

def create_breach_simulation():
    """Create a simulated breach report"""
    report_file = Path.home() / "Desktop" / f"DEMO_SECURITY_BREACH_REPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w') as f:
        f.write("=" * 60 + "\n")
        f.write("🚨 DEMO SECURITY BREACH REPORT 🚨\n")
        f.write("=" * 60 + "\n\n")
        f.write("⚠️  THIS IS A DEMONSTRATION ONLY ⚠️\n\n")
        f.write(f"Document: demo_document.pdf\n")
        f.write(f"Author: Demo User\n")
        f.write(f"Breach Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Failed Attempts: 3\n")
        f.write(f"Computer: {os.environ.get('COMPUTERNAME', 'Unknown')}\n")
        f.write(f"User: {os.environ.get('USERNAME', 'Unknown')}\n\n")
        f.write("SIMULATED SECURITY ACTIONS:\n")
        f.write("• Document access would be permanently disabled\n")
        f.write("• Security tracking files would be securely deleted\n")
        f.write("• Breach event would be logged\n")
        f.write("• Application would be terminated\n\n")
        f.write("📞 In a real scenario, contact the document author immediately.\n\n")
        f.write("=" * 60 + "\n")
        f.write("This report demonstrates what would happen during\n")
        f.write("an actual security breach with the advanced PDF\n")
        f.write("security tool. In production, this would indicate\n")
        f.write("unauthorized access attempts and trigger permanent\n")
        f.write("document destruction protocols.\n")
    
    print(f"📄 Demo breach report created: {report_file}")
    return report_file

def cleanup_demo():
    """Clean up demo files"""
    demo_files = [
        "demo_document.pdf",
        "demo_document.txt"
    ]
    
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️  Cleaned up: {file}")
    
    # Clean up demo security directory
    security_dir = Path.home() / ".pdf_security_demo"
    if security_dir.exists():
        shutil.rmtree(security_dir)
        print(f"🗑️  Cleaned up: {security_dir}")

def main():
    """Run the demonstration"""
    print("🔒 Self-Destruct PDF Security Demonstration")
    print("=" * 50)
    
    while True:
        print("\n📋 DEMO OPTIONS:")
        print("1. 📖 Show security system explanation")
        print("2. 📄 Create demo document")
        print("3. 📁 Show security file locations")
        print("4. 💥 Simulate security breach report")
        print("5. 🗑️  Clean up demo files")
        print("6. 🚀 Run actual security tool (advanced_pdf_security.py)")
        print("7. ❌ Exit demo")
        
        choice = input("\nEnter your choice (1-7): ").strip()
        
        if choice == "1":
            show_demo_instructions()
            
        elif choice == "2":
            create_demo_pdf()
            simulate_security_tracking()
            
        elif choice == "3":
            security_dir = simulate_security_tracking()
            print(f"\n📁 Security files would be stored in:")
            print(f"   {security_dir}")
            print(f"\n📊 Logs would be stored in:")
            print(f"   {security_dir / 'demo_security.log'}")
            
        elif choice == "4":
            create_breach_simulation()
            
        elif choice == "5":
            cleanup_demo()
            
        elif choice == "6":
            print("\n🚀 To test the actual security tool:")
            print("1. Create a demo PDF (option 2)")
            print("2. Run: python advanced_pdf_security.py demo_document.pdf \"Demo User\"")
            print("3. Test with wrong passwords to trigger self-destruct")
            print("\n⚠️  WARNING: This will actually trigger the self-destruct mechanism!")
            break
            
        elif choice == "7":
            print("\n👋 Exiting demonstration. Stay secure!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-7.")

if __name__ == "__main__":
    main() 