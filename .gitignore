# LaTeX auxiliary files
*.aux
*.lof
*.log
*.lot
*.fls
*.out
*.toc
*.fmt
*.fot
*.cb
*.cb2
*.lb
*.dvi
*.xdv
*.bbl
*.bcf
*.blg
*.run.xml
*.fdb_latexmk
*.fls
*.figlist
*.makefile
*.xmpdata
*.auxlock
*.nav
*.snm
*.vrb
*.maf

# LaTeX intermediate files
*.ilg
*.ind
*.idx
*.glo
*.gls
*.glg
*.acn
*.acr
*.alg
*.glsdefs
*.ist
*.lol
*.nlg
*.nlo
*.nls

# LaTeX compilation logs and temporary files
chapitre*.log
compile.log
test_compile.*
texput.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Build directories
*/build/
*/dist/

# Backup files
*_backup_*
*.backup
*.bak

# Protected/Secured PDF files (based on your project)
*_PROTECTED_*
*_SECURE_*
*_SELFDESTRUCT_*

# Temporary files
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
