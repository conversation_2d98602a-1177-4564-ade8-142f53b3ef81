"""workflow_eligibilite.py

Ce script génère un diagramme illustrant le processus décisionnel 
pour déterminer l'éligibilité d'un projet aux dispositifs du Piano Mattei
et de la Charte d'Investissement marocaine.

Le diagramme est exporté en PNG haute résolution dans le dossier figures.
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import FancyArrowPatch, Rectangle
from matplotlib.path import Path
import matplotlib.patheffects as PathEffects

# Configuration
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.size': 10,
    'figure.figsize': (12, 8),
    'figure.dpi': 300,
})

# Couleurs
colors = {
    'decision': '#3498db',
    'process': '#2ecc71',
    'terminal': '#e74c3c',
    'document': '#f39c12',
    'connector': '#9b59b6',
    'background': '#f8f9fa',
    'outline': '#34495e',
    'arrow': '#7f8c8d',
    'text': '#2c3e50',
    'highlight': '#e74c3c',
}

# C<PERSON>er la figure
fig, ax = plt.subplots(figsize=(12, 8))
ax.set_xlim(0, 1000)
ax.set_ylim(0, 650)
ax.axis('off')
ax.set_facecolor(colors['background'])

# Fonction pour dessiner des cases de décision
def draw_decision(x, y, text, w=140, h=60):
    diamond = plt.Polygon([(x, y+h/2), (x+w/2, y+h), (x+w, y+h/2), (x+w/2, y)], 
                         facecolor=colors['decision'], edgecolor=colors['outline'], alpha=0.9)
    ax.add_patch(diamond)
    txt = ax.text(x+w/2, y+h/2, text, ha='center', va='center', fontsize=9, 
                 color='white', wrap=True)
    txt.set_path_effects([PathEffects.withStroke(linewidth=2, foreground='black')])
    return diamond

# Fonction pour dessiner des cases de processus
def draw_process(x, y, text, w=140, h=50):
    rect = plt.Rectangle((x, y), w, h, facecolor=colors['process'], 
                        edgecolor=colors['outline'], alpha=0.9, 
                        linewidth=1.5, linestyle='-')
    ax.add_patch(rect)
    txt = ax.text(x+w/2, y+h/2, text, ha='center', va='center', fontsize=9,
                 color='white', wrap=True)
    txt.set_path_effects([PathEffects.withStroke(linewidth=2, foreground='black')])
    return rect

# Fonction pour dessiner des terminaux
def draw_terminal(x, y, text, w=140, h=50):
    rect = plt.Rectangle((x, y), w, h, facecolor=colors['terminal'], 
                        edgecolor=colors['outline'], alpha=0.9, 
                        linewidth=1.5, linestyle='-')
    ax.add_patch(rect)
    # Ajouter des coins arrondis
    radius = 10
    # Coin supérieur gauche
    corner1 = plt.Circle((x+radius, y+radius), radius, facecolor=colors['terminal'], 
                         edgecolor=colors['outline'], alpha=0.9)
    # Coin supérieur droit
    corner2 = plt.Circle((x+w-radius, y+radius), radius, facecolor=colors['terminal'], 
                         edgecolor=colors['outline'], alpha=0.9)
    # Coin inférieur gauche
    corner3 = plt.Circle((x+radius, y+h-radius), radius, facecolor=colors['terminal'], 
                         edgecolor=colors['outline'], alpha=0.9)
    # Coin inférieur droit
    corner4 = plt.Circle((x+w-radius, y+h-radius), radius, facecolor=colors['terminal'], 
                         edgecolor=colors['outline'], alpha=0.9)
    
    ax.add_patch(corner1)
    ax.add_patch(corner2)
    ax.add_patch(corner3)
    ax.add_patch(corner4)
    
    txt = ax.text(x+w/2, y+h/2, text, ha='center', va='center', fontsize=9,
                 color='white', wrap=True)
    txt.set_path_effects([PathEffects.withStroke(linewidth=2, foreground='black')])
    return rect

# Fonction pour dessiner des documents
def draw_document(x, y, text, w=140, h=50):
    # Version simplifiée - rectangle standard
    document = plt.Rectangle((x, y), w, h, facecolor=colors['document'], 
                           edgecolor=colors['outline'], alpha=0.9, 
                           linewidth=1.5, linestyle='-')
    ax.add_patch(document)
    
    # Petit effet de coin plié (triangle dans le coin supérieur droit)
    fold = plt.Polygon([(x+w-20, y), (x+w, y), (x+w, y+20)], 
                       facecolor='white', edgecolor=colors['outline'], 
                       alpha=0.7, linewidth=1)
    ax.add_patch(fold)
    
    txt = ax.text(x+w/2, y+h/2, text, ha='center', va='center', fontsize=9,
                 color='white', wrap=True)
    txt.set_path_effects([PathEffects.withStroke(linewidth=2, foreground='black')])
    return document

# Fonction pour tracer des flèches avec étiquettes
def draw_arrow(start, end, text='', arrow_style='-|>', color=colors['arrow'], linewidth=1.5):
    arrow = FancyArrowPatch(start, end, arrowstyle=arrow_style, 
                          connectionstyle="arc3,rad=0.1", 
                          color=color, linewidth=linewidth)
    ax.add_patch(arrow)
    if text:
        # Calculer le point médian de la flèche
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2
        # Ajouter un petit décalage pour l'étiquette
        offset = 10
        if start[0] < end[0]:
            mid_x += offset
        else:
            mid_x -= offset
        if start[1] < end[1]:
            mid_y += offset
        else:
            mid_y -= offset
        txt = ax.text(mid_x, mid_y, text, ha='center', va='center', fontsize=8,
                     bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'),
                     color=colors['text'])
    return arrow

# Dessiner les éléments du diagramme
# Départ
start = draw_terminal(450, 600, "Début analyse\nd'éligibilité", w=160)

# ===== BRANCHE PIANO MATTEI =====
# Décision 1: PME italienne?
d1 = draw_decision(250, 520, "Entreprise italienne\nclassifiée PME?", w=160, h=70)
draw_arrow((450+80, 600), (250+80, 520+70), "Évaluation Piano Mattei")

# Sortie si non PME
term1 = draw_terminal(100, 520, "Non éligible au\nvolet PME", w=120)
draw_arrow((250, 520+70/2), (100+120, 520+70/2), "Non")

# Process: Vérification secteur
p1 = draw_process(250, 440, "Vérification secteur\nprioritaire Piano Mattei", w=160)
draw_arrow((250+80, 520), (250+80, 440+50), "Oui")

# Décision 2: Secteur prioritaire?
d2 = draw_decision(250, 360, "Énergie renouvelable\npriorité africaine?", w=160, h=70)
draw_arrow((250+80, 440), (250+80, 360+70))

# Processus: Vérification montant
p2 = draw_process(250, 280, "Vérification montant\nmin. 1M€, max 10M€", w=160)
draw_arrow((250+80, 360), (250+80, 280+50), "Oui")

# Document: SIMEST
doc1 = draw_document(250, 200, "Dossier SIMEST\nformulaire A + B", w=160)
draw_arrow((250+80, 280), (250+80, 200+50))

# Process: Analyse financière
p3 = draw_process(250, 120, "Analyse bancabilité\net rating entreprise", w=160)
draw_arrow((250+80, 200), (250+80, 120+50))

# Terminal: Éligible Piano Mattei
term2 = draw_terminal(250, 40, "Éligible Piano Mattei\n(prêt + subvention)", w=160)
draw_arrow((250+80, 120), (250+80, 40+50))

# ===== BRANCHE MAROC =====
# Décision 3: Projet au Maroc?
d3 = draw_decision(650, 520, "Projet situé\nau Maroc?", w=160, h=70)
draw_arrow((450+80+80, 600), (650+80, 520+70), "Évaluation incitations\nmarocaines")

# Terminal: Non éligible Maroc
term3 = draw_terminal(850, 520, "Non éligible aux\nincitations Maroc", w=120)
draw_arrow((650+160, 520+70/2), (850, 520+70/2), "Non")

# Process: Vérification montant
p4 = draw_process(650, 440, "Vérification montant\n> 50M MAD (≈4.5M€)", w=160)
draw_arrow((650+80, 520), (650+80, 440+50), "Oui")

# Décision 4: Montant suffisant?
d4 = draw_decision(650, 360, "Investissement\n> seuil min?", w=160, h=70)
draw_arrow((650+80, 440), (650+80, 360+70))

# Sortie si montant insuffisant
term4 = draw_terminal(850, 360, "Éligible incitations\nstandard seulement", w=120)
draw_arrow((650+160, 360+70/2), (850, 360+70/2), "Non")

# Process: Vérification zone
p5 = draw_process(650, 280, "Vérification zone\nprioritaire/ZAI", w=160)
draw_arrow((650+80, 360), (650+80, 280+50), "Oui")

# Décision: Projet solaire/éolien?
d5 = draw_decision(500, 240, "Projet solaire/éolien?\nOuarzazate/Dakhla?", w=160, h=70)
draw_arrow((650, 280+25), (500+160, 240+35), "Technologies ENR")

# Document: Charte Investissement
doc2 = draw_document(650, 200, "Dossier CRI\nCharte Investissement", w=160)
draw_arrow((650+80, 280), (650+80, 200+50))

# Process: Évaluation bonus
p6 = draw_process(650, 120, "Évaluation primes\nterritoire/secteur", w=160)
draw_arrow((650+80, 200), (650+80, 120+50))

# Terminal: Éligible primes Maroc
term5 = draw_terminal(650, 40, "Éligible primes Maroc\n(15-30% du CAPEX)", w=160)
draw_arrow((650+80, 120), (650+80, 40+50))

# === Branche MASEN/IRESEN ===
# Document: Dossier MASEN/IRESEN
doc3 = draw_document(500, 160, "Dossier MASEN/IRESEN\nSubventions spécifiques", w=160)
draw_arrow((500+80, 240), (500+80, 160+50), "Oui")

# Terminal: Éligible subventions spécifiques
term6 = draw_terminal(500, 80, "Éligible subventions\nMASEN (10%) / IRESEN (5%)", w=160)
draw_arrow((500+80, 160), (500+80, 80+50))

# Flèche de liaison MASEN/IRESEN -> Primes Maroc
draw_arrow((500+160, 80+25), (650, 40+25), "Cumulable")

# Connexion entre les deux branches
draw_arrow((250+160, 120+25), (650, 120+25), "Croisement des dispositifs")

# Titre
plt.figtext(0.5, 0.95, "PROCESSUS D'ÉLIGIBILITÉ AUX DISPOSITIFS DE FINANCEMENT ITALO-MAROCAINS",
           ha='center', fontsize=14, weight='bold', color=colors['text'])
plt.figtext(0.5, 0.92, "Piano Mattei (Italie), MASEN/IRESEN et Charte de l'Investissement (Maroc)",
           ha='center', fontsize=12, style='italic', color=colors['text'])

# Légende
legend_x, legend_y = 50, 40
rect = plt.Rectangle((legend_x, legend_y), 180, 140, facecolor='white', 
                   edgecolor=colors['outline'], alpha=0.8)
ax.add_patch(rect)
ax.text(legend_x+10, legend_y+120, "LÉGENDE", fontsize=10, weight='bold')

# Éléments de légende
mini_decision = plt.Polygon([(legend_x+20, legend_y+90), (legend_x+40, legend_y+100), 
                           (legend_x+60, legend_y+90), (legend_x+40, legend_y+80)], 
                          facecolor=colors['decision'], edgecolor=colors['outline'])
ax.add_patch(mini_decision)
ax.text(legend_x+70, legend_y+90, "Décision", fontsize=8, va='center')

mini_process = plt.Rectangle((legend_x+20, legend_y+60), 40, 20, 
                           facecolor=colors['process'], edgecolor=colors['outline'])
ax.add_patch(mini_process)
ax.text(legend_x+70, legend_y+70, "Processus", fontsize=8, va='center')

mini_terminal = plt.Rectangle((legend_x+20, legend_y+30), 40, 20, 
                            facecolor=colors['terminal'], edgecolor=colors['outline'])
ax.add_patch(mini_terminal)
ax.text(legend_x+70, legend_y+40, "Terminal", fontsize=8, va='center')

mini_document = plt.Rectangle((legend_x+20, legend_y+10), 40, 20, 
                            facecolor=colors['document'], edgecolor=colors['outline'])
ax.add_patch(mini_document)
ax.text(legend_x+70, legend_y+20, "Document", fontsize=8, va='center')

# Sauvegarder le diagramme
plt.savefig('figures/workflow_eligibilite.png', dpi=300, bbox_inches='tight', 
           facecolor=colors['background'])
print("Diagramme d'éligibilité généré avec succès!")
plt.close()
