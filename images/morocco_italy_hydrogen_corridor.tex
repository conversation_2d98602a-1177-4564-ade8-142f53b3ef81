\documentclass[crop,tikz]{standalone}
\usepackage{tikz}
\usepackage{xcolor}
\usetikzlibrary{decorations.pathmorphing,decorations.pathreplacing,calc}

\begin{document}
\begin{tikzpicture}
    % Define colors
    \definecolor{water}{RGB}{210,230,250}
    \definecolor{land1}{RGB}{240,230,200}
    \definecolor{land2}{RGB}{230,220,190}
    \definecolor{pipeline}{RGB}{0,128,0}
    
    % Mediterranean Sea
    \fill[water] (-8,-2) rectangle (8,5);
    
    % Italy (simplified)
    \fill[land1] (5,0) -- (6,0.5) -- (5.5,2) -- (6.5,3) -- (5,4) -- (4,3) -- (3,3.5) -- (3,2) -- (4,0.5) -- (5,0);
    \node[font=\bfseries] at (5,2.5) {ITALIE};
    
    % Trieste marker
    \filldraw[red] (3.5,3) circle (0.15);
    \node[anchor=west, font=\small\bfseries] at (3.7,3) {Trieste};
    
    % Africa (simplified)
    \fill[land2] (-7,-2) -- (-6,-2) -- (-5,-1) -- (-4,-1.5) -- (-2,-0.5) -- (0,-1) -- (2,-0.5) -- (3,-1) -- (2,-2) -- (-7,-2);
    
    % Morocco
    \fill[land1] (-6,-2) -- (-5,-1) -- (-4,-1.5) -- (-4,-2) -- (-6,-2);
    \node[font=\bfseries] at (-5,-1.5) {MAROC};
    
    % Pipeline
    \draw[pipeline, ultra thick, decoration={snake, amplitude=0.5mm, segment length=4mm}, decorate] 
        (-5,-1.5) -- (3.5,3);
        
    % Legend
    \draw[pipeline, ultra thick, decoration={snake, amplitude=0.5mm, segment length=4mm}, decorate] 
        (-7,4) -- (-6,4);
    \node[anchor=west] at (-5.8,4) {Corridor d'hydrogène vert};
    
    % Production sites
    \filldraw[blue] (-5,-1.5) circle (0.15);
    \node[anchor=south, font=\small] at (-5,-1.3) {Production H₂};
    
    % Scale
    \draw[thick] (-7,-1) -- (-4,-1);
    \node[anchor=north] at (-5.5,-1.1) {500 km};
    
    % Title
    \node[font=\large\bfseries] at (0,4.5) {Corridor d'Hydrogène Vert Maroc-Trieste};
    
    % Notes
    \node[anchor=north west, align=left, text width=5cm, font=\small] at (-7.5,-1.8) 
        {Transport jusqu'à 500 000 tonnes\\d'hydrogène vert par an\\Infrastructure estimée à 4,5 milliards €};
\end{tikzpicture}
\end{document} 