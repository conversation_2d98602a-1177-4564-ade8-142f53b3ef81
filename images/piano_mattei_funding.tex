\documentclass[crop,tikz]{standalone}
\usepackage{tikz}
\usepackage{xcolor}

\begin{document}
\begin{tikzpicture}
    % Define colors
    \definecolor{climate}{RGB}{46,139,87}
    \definecolor{development}{RGB}{70,130,180}
    
    % Draw pie chart
    \draw[fill=climate] (0,0) -- (0:3) arc (0:195:3) -- cycle;
    \draw[fill=development] (0,0) -- (195:3) arc (195:360:3) -- cycle;
    
    % Labels
    \node[text width=4cm, align=center, font=\bfseries] at (70:2) {Fonds italien\\pour le climat\\3 milliards €};
    \node[text width=4cm, align=center, font=\bfseries] at (280:2) {Fonds de coopération\\au développement\\2,5 milliards €};
    
    % Total
    \node[font=\large\bfseries] at (0,-4) {Total: 5,5 milliards d'euros};
    
    % Legend
    \draw[fill=climate] (-4,-3) rectangle (-3.5,-2.5);
    \node[anchor=west] at (-3.3,-2.75) {Fonds italien pour le climat};
    
    \draw[fill=development] (-4,-4) rectangle (-3.5,-3.5);
    \node[anchor=west] at (-3.3,-3.75) {Fonds de coopération au développement};
\end{tikzpicture}
\end{document} 