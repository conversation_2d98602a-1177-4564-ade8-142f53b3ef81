% Simple Bar Chart for Renewable Energy Capacity in North Africa (2023)

\begin{tikzpicture}
    % Define colors with simpler syntax
    \colorlet{morocco}{blue!70}
    \colorlet{egypt}{red!80}
    \colorlet{tunisia}{orange!80}
    \colorlet{algeria}{green!60}
    \colorlet{libya}{gray!60}
    
    % Set up the axes with simpler design
    \draw[thick] (0,0) -- (0,6);
    \draw[thick] (0,0) -- (10,0);
    
    % Add labels for axes
    \node[rotate=90, above] at (-0.5,3) {Capacité installée (GW)};
    
    % Draw horizontal grid lines
    \foreach \y in {0,1,...,5}
        \draw[dotted, gray] (0,\y) -- (10,\y);
    
    % Add numerical labels for y-axis
    \foreach \y in {0,1,...,5}
        \node[left] at (0,\y) {\y};
    
    % Draw bars with simpler coordinates
    \fill[morocco] (0.5,0) rectangle (2,4.1);
    \fill[egypt] (2.5,0) rectangle (4,3.5);
    \fill[tunisia] (4.5,0) rectangle (6,0.8);
    \fill[algeria] (6.5,0) rectangle (8,0.7);
    \fill[libya] (8.5,0) rectangle (10,0.1);
    
    % Add country labels
    \node[below] at (1.25,-0.2) {Maroc};
    \node[below] at (3.25,-0.2) {Égypte};
    \node[below] at (5.25,-0.2) {Tunisie};
    \node[below] at (7.25,-0.2) {Algérie};
    \node[below] at (9.25,-0.2) {Libye};
    
    % Add capacity values
    \node[above] at (1.25,4.1) {4,1};
    \node[above] at (3.25,3.5) {3,5};
    \node[above] at (5.25,0.8) {0,8};
    \node[above] at (7.25,0.7) {0,7};
    \node[above] at (9.25,0.1) {0,1};
    
    % Add legend at bottom
    \node[below] at (5,-1) {\small Capacités en GW. Source: IRENA, 2024};
\end{tikzpicture}
